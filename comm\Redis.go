package comm

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/Mmtls"
	"wechatdll/baseinfo"
	"wechatdll/models"

	"github.com/astaxie/beego"
	"github.com/go-redis/redis"
	log "github.com/sirupsen/logrus"
)

// LoginDataInfo 62/16 数据登录
type LoginDataInfo struct {
	Type     byte
	UserName string
	PassWord string
	//伪密码
	NewPassWord string
	//登录数据 62/A16
	LoginData string
	Ticket    string
	NewType   int
	Language  string
}

var RedisClient *redis.Client

type LoginData struct {
	Uin                        uint32
	Wxid                       string
	Pwd                        string
	Uuid                       string
	Aeskey                     []byte
	NotifyKey                  []byte
	Deviceid_str               string
	Deviceid_byte              []byte
	DeviceType                 string
	ClientVersion              int32
	DeviceName                 string
	NickName                   string
	HeadUrl                    string
	Email                      string
	Alais                      string
	Mobile                     string
	Mmtlsip                    string
	ShortHost                  string
	LongHost                   string
	Sessionkey                 []byte
	Sessionkey_2               []byte
	Autoauthkey                []byte
	Autoauthkeylen             int32
	Clientsessionkey           []byte
	Serversessionkey           []byte
	HybridEcdhPrivkey          []byte
	HybridEcdhPubkey           []byte
	HybridEcdhInitServerPubKey []byte
	Loginecdhkey               []byte
	Cooike                     []byte
	LoginMode                  string
	Proxy                      models.ProxyInfo
	MmtlsKey                   *Mmtls.MmtlsClient
	DeviceToken                *mm.TrustResponse
	SyncKey                    []byte
	Data62                     string
	RomModel                   string
	Imei                       string
	SoftType                   string
	OsVersion                  string
	RsaPublicKey               []byte
	RsaPrivateKey              []byte
	Dns                        []Dns
	MmtlsHost                  string
	// 登录的Rsa 密钥版本
	LoginRsaVer uint32
	// 是否开启服务
	EnableService bool
	EcPublicKey   []byte `json:"ecpukey"`
	EcPrivateKey  []byte `json:"ecprkey"`
	Ticket        string
	LoginDataInfo LoginDataInfo
	// 设备信息62
	DeviceInfo *baseinfo.DeviceInfo
	//A16信息
	DeviceInfoA16 *baseinfo.AndroidDeviceInfo
	// 登录时间
	LoginDate int64
	// 刷新 tonken 时间
	RefreshTokenDate int64
}

func (u *LoginData) GetDeviceInfoA16() *baseinfo.AndroidDeviceInfo {
	if u.DeviceInfoA16 == nil {
		u.DeviceInfoA16 = &baseinfo.AndroidDeviceInfo{}
		u.DeviceInfoA16.BuildBoard = "bullhead"
	}
	return u.DeviceInfoA16

}

func (u *LoginData) GetNickName() string {
	return u.NickName
}

// GetUserName 取用户账号信息
func (u *LoginData) GetUserName() string {
	if u.Wxid == "" {
		return u.LoginDataInfo.UserName
	} else {
		return u.Wxid
	}
}

// LoginRsaVer 登录用到的RSA版本号
var LoginRsaVer = uint32(135)

var XJLoginRSAVer = uint32(133)

// DefaultLoginRsaVer 默认 登录RSA版本号
var DefaultLoginRsaVer = LoginRsaVer

// Md5OfMachOHeader wechat的MachOHeader md5值 4c541f4fca66dd93a351d4239ecaf7ae
var Md5OfMachOHeader = string("d05a80a94b6c2e3c31424403437b6e18") //

// FileHelperWXID 文件传输助手微信ID
var FileHelperWXID = string("filehelper")

// HomeDIR 当前程序的工作路径
var HomeDIR string

func (u LoginData) GetLoginRsaVer() uint32 {
	if u.LoginRsaVer == 0 {
		u.LoginRsaVer = DefaultLoginRsaVer
	}
	return u.LoginRsaVer
}

type Dns struct {
	Ip   string
	Host string
}

type DeviceTokenKey struct {
}

func GETObj(key string, i any) error {
	// 清理首尾空格
	key = strings.TrimSpace(key)
	if key == "" {
		return fmt.Errorf("key不能为空")
	}

	val, err := RedisClient.Get(key).Result()
	if err != nil {
		return fmt.Errorf("Redis获取数据失败: %w", err)
	}

	if val == "" {
		return fmt.Errorf("key [%s] 对应的数据为空", key)
	}

	if err := json.Unmarshal([]byte(val), i); err != nil {
		return fmt.Errorf("JSON反序列化失败: %w", err)
	}

	return nil
}

// 自动心跳列表，每个 id 存 100 条最新的
var AutoHeartBeatList = make(map[string][]string)
var AutoHeartBeatListLock = make(chan int, 1)

func AutoHeartBeatListAdd(wxid string, content string) {
	AutoHeartBeatListLock <- 1
	defer func() {
		<-AutoHeartBeatListLock
	}()
	if _, ok := AutoHeartBeatList[wxid]; !ok {
		AutoHeartBeatList[wxid] = make([]string, 0)
	}
	AutoHeartBeatList[wxid] = append([]string{content}, AutoHeartBeatList[wxid]...)
	if len(AutoHeartBeatList[wxid]) > 100 {
		AutoHeartBeatList[wxid] = AutoHeartBeatList[wxid][:100]
	}
	SETExpirationObj("AutoHeartBeatList:"+wxid, AutoHeartBeatList[wxid], 0)
}

// 清理心跳包
func AutoHeartBeatListClear(wxid string) {
	AutoHeartBeatListLock <- 1
	defer func() {
		<-AutoHeartBeatListLock
	}()
	delete(AutoHeartBeatList, wxid)
	RedisClient.Del("AutoHeartBeatList:" + wxid)
}

// 从 redis 中初始化全部用户的心跳包， return 出去
func GetAutoHeartBeatList() map[string][]string {
	AutoHeartBeatListLock <- 1
	defer func() {
		<-AutoHeartBeatListLock
	}()
	temAutoHeartBeatList := make(map[string][]string)
	AutoHeartBeatListKeys, _ := RedisClient.Keys("AutoHeartBeatList:*").Result()
	for _, key := range AutoHeartBeatListKeys {
		AutoHeartBeatList[key] = make([]string, 0)
		item := make([]string, 0)
		GETObj(key, &item)
		temAutoHeartBeatList[key] = item
	}
	return temAutoHeartBeatList
}

func Exists(k string) bool {
	//检查是否存在key值
	exists := RedisClient.Exists(k)
	if exists.Val() > 0 {
		return true
	}
	return false
}

func SETExpirationObj(k string, i any, expiration int64) error {
	// 清理首尾空格
	k = strings.TrimSpace(k)
	if k == "" {
		return fmt.Errorf("key不能为空")
	}

	iData, err := json.Marshal(i) // 移除不必要的指针引用
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %w", err)
	}

	var duration time.Duration
	if expiration > 0 {
		duration = time.Duration(expiration) * time.Second
	} else {
		duration = 0 // 永不过期
	}

	if err := RedisClient.Set(k, string(iData), duration).Err(); err != nil {
		return fmt.Errorf("Redis设置数据失败: %w", err)
	}

	return nil
}

func RedisInitialize() *redis.Client {
	dbNum, err := beego.AppConfig.Int("redisdbnum")
	if err != nil {
		log.Errorf("读取redisdbnum配置失败: %v", err)
		dbNum = 0 // 使用默认值
	}

	// 优化Redis连接配置
	RedisClient = redis.NewClient(&redis.Options{
		Addr:         beego.AppConfig.String("redislink"),                                                // redis地址
		Password:     beego.AppConfig.String("redispass"),                                                // redis密码，没有则留空
		DB:           dbNum,                                                                              // 默认数据库，默认是0
		PoolSize:     beego.AppConfig.DefaultInt("redis_pool_size", 50),                                  // 连接池大小
		MinIdleConns: beego.AppConfig.DefaultInt("redis_min_idle_conns", 10),                             // 最小空闲连接数
		MaxRetries:   beego.AppConfig.DefaultInt("redis_max_retries", 5),                                 // 最大重试次数
		DialTimeout:  time.Duration(beego.AppConfig.DefaultInt("redis_dial_timeout", 10)) * time.Second,  // 连接超时
		ReadTimeout:  time.Duration(beego.AppConfig.DefaultInt("redis_read_timeout", 5)) * time.Second,   // 读取超时
		WriteTimeout: time.Duration(beego.AppConfig.DefaultInt("redis_write_timeout", 5)) * time.Second,  // 写入超时
		IdleTimeout:  time.Duration(beego.AppConfig.DefaultInt("redis_idle_timeout", 600)) * time.Second, // 空闲超时
		PoolTimeout:  time.Duration(beego.AppConfig.DefaultInt("redis_pool_timeout", 10)) * time.Second,  // 连接池获取连接超时
	})

	// 测试Redis连接
	if err := testRedisConnection(RedisClient); err != nil {
		log.Errorf("Redis连接测试失败: %v", err)
		// 不要panic，让程序继续运行，但记录错误
	}

	// 启动Redis连接健康检查
	go startRedisHealthCheck(RedisClient)

	return RedisClient
}

// 为每个 key 加锁

var groupMu sync.Mutex
var groupMap = make(map[string]*sync.Mutex)
var groupMapLock = make(chan int, 1)

func GetLoginLock(key string) *sync.Mutex {
	groupMapLock <- 1
	defer func() {
		<-groupMapLock
	}()
	// 去掉头尾空格
	key = strings.TrimSpace(key)
	if key == "" {
		key = "default"
	}
	groupMu.Lock()
	defer groupMu.Unlock()
	if _, ok := groupMap[key]; !ok {
		groupMap[key] = &sync.Mutex{}
	}
	return groupMap[key]
}

// 保存redis缓存, 如果Expiration大于0, 则有限临时缓存, 等于0持久缓存, 小于0无限临时缓存
func CreateLoginData(data *LoginData, key string, Expiration int64, temMu *sync.Mutex) error {
	// 存和取同时上锁，避免并发覆盖
	mu := GetLoginLock(key)
	if temMu != mu {
		mu.Lock()
		defer mu.Unlock()
	}
	var ExpTime time.Duration
	// TODO(kedaya): 确认Redis键前缀策略 - 已优化
	// Redis键前缀策略：区分持久化和临时数据，避免键冲突
	// PERSISTENT: 持久保存的登录数据
	// TEMPORARY: 临时保存的登录数据（有过期时间）
	const (
		PERSISTENT_PREFIX = "PERSISTENT:"
		TEMPORARY_PREFIX  = "TEMPORARY:"
	)

	prefixStr := PERSISTENT_PREFIX
	if key == "" {
		key = data.Uuid
	}

	if Expiration > 0 {
		ExpTime = time.Second * time.Duration(Expiration)
		prefixStr = TEMPORARY_PREFIX
	} else {
		ExpTime = 0
		if Expiration < 0 {
			prefixStr = TEMPORARY_PREFIX
		}
	}

	log.WithFields(log.Fields{
		"prefix":     prefixStr,
		"key":        key,
		"expiration": Expiration,
	}).Debug("Redis键前缀策略")
	JsonData, _ := json.Marshal(&data)
	err := RedisClient.Set(key, string(JsonData), ExpTime).Err()
	if err != nil {
		return err
	}
	return nil
}

func CreateLogin(data LoginData, key string, Expiration int64) error {
	var ExpTime time.Duration
	// 使用统一的Redis键前缀策略
	const (
		PERSISTENT_PREFIX = "PERSISTENT:"
		TEMPORARY_PREFIX  = "TEMPORARY:"
	)

	prefixStr := PERSISTENT_PREFIX
	if key == "" {
		key = data.Uuid
	}

	if Expiration > 0 {
		ExpTime = time.Second * time.Duration(Expiration)
		prefixStr = TEMPORARY_PREFIX
	} else {
		ExpTime = 0
		if Expiration < 0 {
			prefixStr = TEMPORARY_PREFIX
		}
	}

	log.WithFields(log.Fields{
		"prefix":     prefixStr,
		"key":        key,
		"expiration": Expiration,
	}).Debug("Redis键前缀策略")
	JsonData, _ := json.Marshal(&data)
	err := RedisClient.Set(key, string(JsonData), ExpTime).Err()
	if err != nil {
		return err
	}
	return nil
}

func GetKeyJsonData(Key string) (ret string, err error) {
	// 使用统一的Redis键前缀策略
	const (
		PERSISTENT_PREFIX = "PERSISTENT:"
		TEMPORARY_PREFIX  = "TEMPORARY:"
		// 兼容旧版本前缀
		LEGACY_PERSISTENT_PREFIX = "PERM1:"
		LEGACY_TEMPORARY_PREFIX  = "TEMP1:"
	)

	// 优先读取新版本持久键值
	val, _ := RedisClient.Get(PERSISTENT_PREFIX + Key).Result()
	if val != "" {
		return val, nil
	}

	// 兼容旧版本持久键值
	val, _ = RedisClient.Get(LEGACY_PERSISTENT_PREFIX + Key).Result()
	if val != "" {
		return val, nil
	}

	// 兼容原版无前缀键值
	val, _ = RedisClient.Get(Key).Result()
	if val != "" {
		return val, nil
	}

	// 读取新版本临时键值
	val, _ = RedisClient.Get(TEMPORARY_PREFIX + Key).Result()
	if val != "" {
		return val, nil
	}

	// 兼容旧版本临时键值
	val, _ = RedisClient.Get(LEGACY_TEMPORARY_PREFIX + Key).Result()
	if val == "" {
		return ret, fmt.Errorf("key [%v] 数据不存在", Key)
	}
	return val, nil
}

func GetLoginata(key string, temMu *sync.Mutex) (*LoginData, error) {
	// 存和取同时上锁，避免并发覆盖
	mu := GetLoginLock(key)
	if temMu != mu {
		mu.Lock()
		defer mu.Unlock()
	}
	loginData := &LoginData{}
	P, err := GetKeyJsonData(key)
	if err == nil {
		_ = json.Unmarshal([]byte(P), loginData)
	}
	// 确保 ShortHost 和 LongHost 不为空
	if loginData.ShortHost == "" {
		loginData.ShortHost = Algorithm.MmtlsShortHost
	}
	if loginData.LongHost == "" {
		loginData.LongHost = Algorithm.MmtlsLongHost
	}
	return loginData, nil
}

func GetLoginatas(key string) (*LoginData, error) {
	P, err := GetKeyJsonData(key)
	if err != nil {
		return &LoginData{}, err
	}
	D := &LoginData{}
	err = json.Unmarshal([]byte(P), D)
	if err != nil {
		return &LoginData{}, err
	}

	return D, nil
}
func GetLoginataByDevId(key string) (*LoginData, error) {
	// 清理首尾空格
	key = strings.TrimSpace(key)
	if key == "" || key == "string" {
		return &LoginData{}, nil
	}
	// 根据 设备ID获取 userName
	userName, err := RedisClient.Get("devId:" + key).Result()
	if err != nil {
		return &LoginData{}, err
	}
	return GetLoginata(userName, nil)
}

func DelLoginata(key string) error {
	return RedisClient.Del(key).Err()
}

/*
*
设置今天抢红包的数额

	1 表示红包
	2 表示转账
*/
func SetTodayMoney(key string, fieldKey string, data float64, dataType int) error {
	prefixStr := ""
	switch dataType {
	case 1:
		{
			prefixStr = "wxhb:"
			break
		}
	case 2:
		{
			prefixStr = "wxzz:"
			break
		}

	}
	moneyKey := prefixStr + key
	// 首先获取今天的金额
	todayMoney, _ := RedisClient.HGet(moneyKey, fieldKey).Float64()
	totalMoney := todayMoney + data
	err := RedisClient.HSet(moneyKey, fieldKey, totalMoney).Err()
	if err != nil {
		return err
	}
	return nil
}

// hash写入数据
func GetTodayMoney(key string, dataType int) map[string]string {
	prefixStr := ""
	switch dataType {
	case 1:
		{
			prefixStr = "wxhb:"
			break
		}
	case 2:
		{
			prefixStr = "wxzz:"
			break
		}

	}
	moneyKey := prefixStr + key

	// 优先读取持久键值
	//cmd, _ := RedisClient.HGetAll(moneyKey)
	cmd := RedisClient.HGetAll(moneyKey)
	result, err := cmd.Result()
	if err != nil {
		return nil
	}
	return result

}

// testRedisConnection 测试Redis连接
func testRedisConnection(client *redis.Client) error {
	// 执行PING命令测试连接
	pong, err := client.Ping().Result()
	if err != nil {
		return fmt.Errorf("Redis PING失败: %v", err)
	}

	if pong != "PONG" {
		return fmt.Errorf("Redis PING响应异常: %s", pong)
	}

	return nil
}

// startRedisHealthCheck 启动Redis健康检查
func startRedisHealthCheck(client *redis.Client) {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := testRedisConnection(client); err != nil {
				log.Errorf("Redis健康检查失败: %v", err)
				// 可以在这里添加重连逻辑或告警
			}
		}
	}
}

// SafeRedisOperation 安全的Redis操作包装器
func SafeRedisOperation(operation func() error, operationName string) error {
	maxRetries := 3
	baseDelay := 100 * time.Millisecond

	for i := 0; i < maxRetries; i++ {
		err := operation()
		if err == nil {
			return nil
		}

		// 检查是否是连接相关错误
		if isConnectionError(err) {
			log.Warnf("Redis %s 操作失败 (尝试 %d/%d): %v", operationName, i+1, maxRetries, err)

			if i < maxRetries-1 {
				// 指数退避
				delay := baseDelay * time.Duration(1<<uint(i))
				time.Sleep(delay)
				continue
			}
		}

		return fmt.Errorf("Redis %s 操作最终失败: %v", operationName, err)
	}

	return fmt.Errorf("Redis %s 操作超过最大重试次数", operationName)
}

// isConnectionError 判断是否是连接相关错误
func isConnectionError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// 排除非连接错误：键不存在、数据为空等正常业务错误
	nonConnectionErrors := []string{
		"redis: nil",
		"key [",
		"对应的数据为空",
		"数据不存在",
		"JSON反序列化失败",
		"JSON序列化失败",
	}

	for _, nonConnErr := range nonConnectionErrors {
		if strings.Contains(errStr, nonConnErr) {
			return false
		}
	}

	connectionErrors := []string{
		"connection refused",
		"connection reset",
		"broken pipe",
		"timeout",
		"i/o timeout",
		"connection pool exhausted",
		"connection closed",
	}

	for _, connErr := range connectionErrors {
		if strings.Contains(strings.ToLower(errStr), connErr) {
			return true
		}
	}

	return false
}
