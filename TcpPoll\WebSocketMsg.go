// 本包实现了微信消息的WebSocket实时推送功能，包括：
// - WebSocket连接管理
// - 消息格式化和推送
// - 自动功能处理（红包领取、收款确认等）
// - 定时消息同步
// - 错误处理和恢复机制
//
// 主要功能：
// 1. 管理多个微信账号的WebSocket连接
// 2. 实时推送微信消息到前端
// 3. 解析和格式化各种类型的微信消息
// 4. 提供消息同步和测试功能
//
// 使用示例：
//
//	// 初始化WebSocket服务
//	InitWs()
//
//	// 发送消息到指定客户端
//	SendTestMessage("wxid_example")
//
//	// 向所有客户端广播消息
//	SendMessageToAllClients(message)
package TcpPoll

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"wechatdll/Cilent/mm"

	"github.com/astaxie/beego"
	"github.com/gorilla/websocket"
	log "github.com/sirupsen/logrus"
)

// ==================== 常量定义 ====================

const (
	// WebSocket 配置常量
	DefaultWSPort     = "8088"
	MaxReadLimit      = 1024 * 1024        // 1MB
	ReadTimeout       = 24 * time.Hour     // 设置24小时超时，实际上相当于不超时
	WriteTimeout      = 30 * time.Second   // 写入超时30秒
	HeartbeatInterval = 60 * time.Second   // 心跳间隔（已禁用）
	PingTimeout       = 10 * time.Second   // ping超时（已禁用）

	// 消息同步配置
	SyncInterval    = 5 * time.Second
	SyncLogInterval = 1 // 每1次同步输出一次日志

	// API 端点
	SyncAPIEndpoint = "http://localhost:8059/api/msg/SyncAndPush"

	// 消息类型常量（保持向后兼容）
	MsgTypeText         = 1
	MsgTypeImage        = 3
	MsgTypeVoice        = 34
	MsgTypeVideo        = 43
	MsgTypeEmoji        = 47
	MsgTypeLocation     = 48
	MsgTypeLink         = 49
	MsgTypeCard         = 42
	MsgTypeStatusNotify = 51
	MsgTypeSystem       = 10000
	MsgTypeUnknown9999  = 9999
)

var msgTypeDescriptions = map[uint32]string{
	MsgTypeText:         "文本消息",
	MsgTypeImage:        "图片消息",
	MsgTypeVoice:        "语音消息",
	MsgTypeVideo:        "视频消息",
	MsgTypeCard:         "名片消息",
	MsgTypeEmoji:        "表情消息",
	MsgTypeLocation:     "位置消息",
	MsgTypeLink:         "链接/小程序/文件",
	MsgTypeStatusNotify: "状态通知",
	MsgTypeSystem:       "系统消息",
	MsgTypeUnknown9999:  "系统配置更新",
}

// ==================== 错误类型定义 ====================

// WebSocketError WebSocket相关错误
type WebSocketError struct {
	Type    string
	Message string
	Wxid    string
	Err     error
}

func (e *WebSocketError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("[%s] %s (wxid: %s): %v", e.Type, e.Message, e.Wxid, e.Err)
	}
	return fmt.Sprintf("[%s] %s (wxid: %s)", e.Type, e.Message, e.Wxid)
}

// 错误类型常量
const (
	ErrorTypeConnection = "CONNECTION"
	ErrorTypeMessage    = "MESSAGE"
	ErrorTypeSync       = "SYNC"
	ErrorTypeAuth       = "AUTH"
	ErrorTypeInternal   = "INTERNAL"
)

// 创建错误的辅助函数
func newWebSocketError(errorType, message, wxid string, err error) *WebSocketError {
	return &WebSocketError{
		Type:    errorType,
		Message: message,
		Wxid:    wxid,
		Err:     err,
	}
}

// ==================== 连接管理器 ====================

// ConnectionManager 连接管理器，封装所有连接相关的操作
type ConnectionManager struct {
	clients        sync.Map // map[string]*websocket.Conn，使用sync.Map减少锁竞争
	clientMetadata sync.Map // map[string]*ClientMetadata
	maxConnections int32
	shutdownChan   chan struct{}

	// 性能统计（使用原子操作）
	connectionCount int64
	totalMessages   int64
	totalErrors     int64

	// context用于优雅关闭
	ctx    context.Context
	cancel context.CancelFunc

	// 消息处理工作池
	messagePool *WorkerPool
}

// 全局连接管理器实例
var connManager *ConnectionManager

// ClientMetadata 客户端元数据
type ClientMetadata struct {
	ConnectTime   time.Time
	LastHeartbeat time.Time
	MessageCount  int64 // 使用原子操作
	LastActivity  time.Time
	ErrorCount    int64  // 错误计数
	UserAgent     string // 用户代理
	RemoteAddr    string // 远程地址
}

// WorkerPool 工作池，用于限制goroutine数量
type WorkerPool struct {
	workers   chan chan func()
	workQueue chan func()
	quit      chan bool
	wg        sync.WaitGroup
}

// NewWorkerPool 创建新的工作池
func NewWorkerPool(maxWorkers int, maxQueue int) *WorkerPool {
	pool := &WorkerPool{
		workers:   make(chan chan func(), maxWorkers),
		workQueue: make(chan func(), maxQueue),
		quit:      make(chan bool),
	}

	// 启动工作者
	for i := 0; i < maxWorkers; i++ {
		worker := &Worker{
			workerPool: pool.workers,
			jobChannel: make(chan func()),
			quit:       make(chan bool),
		}
		worker.Start()
	}

	// 启动调度器
	go pool.dispatch()
	return pool
}

// Worker 工作者
type Worker struct {
	workerPool chan chan func()
	jobChannel chan func()
	quit       chan bool
}

// Start 启动工作者
func (w *Worker) Start() {
	go func() {
		for {
			w.workerPool <- w.jobChannel
			select {
			case job := <-w.jobChannel:
				job()
			case <-w.quit:
				return
			}
		}
	}()
}

// dispatch 调度任务
func (p *WorkerPool) dispatch() {
	for {
		select {
		case job := <-p.workQueue:
			go func() {
				jobChannel := <-p.workers
				jobChannel <- job
			}()
		case <-p.quit:
			return
		}
	}
}

// Submit 提交任务
func (p *WorkerPool) Submit(job func()) {
	select {
	case p.workQueue <- job:
	default:
		// 队列满时直接执行，避免阻塞
		go job()
	}
}

// Stop 停止工作池
func (p *WorkerPool) Stop() {
	close(p.quit)
	p.wg.Wait()
}

// WebSocket 升级器配置
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 生产环境应该检查Origin头
		origin := r.Header.Get("Origin")
		if origin == "" {
			return true // 允许没有Origin的请求（如Postman等工具）
		}
		// 这里可以添加白名单检查
		return true
	},
	ReadBufferSize:    4096, // 增加缓冲区大小
	WriteBufferSize:   4096, // 增加缓冲区大小
	EnableCompression: true, // 启用压缩
}

// ==================== 连接管理器方法 ====================

// NewConnectionManager 创建新的连接管理器
func NewConnectionManager(maxConnections int32) *ConnectionManager {
	ctx, cancel := context.WithCancel(context.Background())

	cm := &ConnectionManager{
		maxConnections: maxConnections,
		shutdownChan:   make(chan struct{}),
		ctx:            ctx,
		cancel:         cancel,
		messagePool:    NewWorkerPool(50, 1000), // 50个工作者，1000个任务队列
	}

	return cm
}

// RegisterClient 注册客户端连接（线程安全）
func (cm *ConnectionManager) RegisterClient(wxid string, conn *websocket.Conn, r *http.Request) error {
	// 检查连接数限制
	currentCount := atomic.LoadInt64(&cm.connectionCount)
	if currentCount >= int64(cm.maxConnections) {
		return newWebSocketError(ErrorTypeConnection, "连接数已达上限", wxid, nil)
	}

	// 如果客户端已存在，先清理旧连接
	if oldConnInterface, exists := cm.clients.LoadAndDelete(wxid); exists {
		if oldConn, ok := oldConnInterface.(*websocket.Conn); ok {
			log.Warnf("客户端重复连接，关闭旧连接: wxid=%s", wxid)
			oldConn.Close()
		}
		cm.clientMetadata.Delete(wxid)
	}

	// 提取客户端信息
	userAgent := r.Header.Get("User-Agent")
	remoteAddr := r.RemoteAddr
	if forwarded := r.Header.Get("X-Forwarded-For"); forwarded != "" {
		remoteAddr = forwarded
	}

	// 注册新连接
	cm.clients.Store(wxid, conn)
	cm.clientMetadata.Store(wxid, &ClientMetadata{
		ConnectTime:   time.Now(),
		LastHeartbeat: time.Now(),
		MessageCount:  0,
		LastActivity:  time.Now(),
		ErrorCount:    0,
		UserAgent:     userAgent,
		RemoteAddr:    remoteAddr,
	})

	atomic.AddInt64(&cm.connectionCount, 1)

	log.WithFields(log.Fields{
		"wxid":        wxid,
		"user_agent":  userAgent,
		"remote_addr": remoteAddr,
	}).Info("客户端已连接")

	return nil
}

// UnregisterClient 注销客户端连接
func (cm *ConnectionManager) UnregisterClient(wxid string) {
	if metadataInterface, exists := cm.clientMetadata.LoadAndDelete(wxid); exists {
		if metadata, ok := metadataInterface.(*ClientMetadata); ok {
			duration := time.Since(metadata.ConnectTime)
			log.WithFields(log.Fields{
				"wxid":          wxid,
				"duration":      duration,
				"message_count": atomic.LoadInt64(&metadata.MessageCount),
				"error_count":   atomic.LoadInt64(&metadata.ErrorCount),
			}).Info("客户端已断开")
		}
	}

	cm.clients.Delete(wxid)
	atomic.AddInt64(&cm.connectionCount, -1)
}

// GetClient 获取客户端连接
func (cm *ConnectionManager) GetClient(wxid string) (*websocket.Conn, bool) {
	if connInterface, exists := cm.clients.Load(wxid); exists {
		if conn, ok := connInterface.(*websocket.Conn); ok {
			return conn, true
		}
	}
	return nil, false
}

// GetAllClients 获取所有客户端列表
func (cm *ConnectionManager) GetAllClients() []string {
	var clients []string
	cm.clients.Range(func(key, value interface{}) bool {
		if wxid, ok := key.(string); ok {
			clients = append(clients, wxid)
		}
		return true
	})
	return clients
}

// GetConnectionCount 获取连接数
func (cm *ConnectionManager) GetConnectionCount() int64 {
	return atomic.LoadInt64(&cm.connectionCount)
}

// ==================== 初始化函数 ====================

// InitWs 初始化并启动WebSocket服务
func InitWs() {
	// 初始化连接管理器
	connManager = NewConnectionManager(1000)

	// 注册WebSocket处理器
	http.HandleFunc("/ws", handleWebSocket)
	http.HandleFunc("/ws/health", handleHealthCheck)
	http.HandleFunc("/ws/stats", handleStatsCheck)

	// 获取配置
	wsPort := beego.AppConfig.DefaultString("wsport", DefaultWSPort)
	enableTestMessages := beego.AppConfig.DefaultBool("ws_enable_test_messages", false)
	enableAutoSync := beego.AppConfig.DefaultBool("ws_enable_auto_sync", true)

	log.Infof("WebSocket服务启动在端口: %s", wsPort)

	// 启动可选的定时消息发送器（用于测试）
	if enableTestMessages {
		SafeGoroutine("periodicMessageSender", periodicMessageSender)
	} else {
		log.Info("WebSocket定时测试消息已禁用（可在配置中启用）")
	}

	// 启动定时消息同步器
	if enableAutoSync {
		SafeGoroutine("periodicMessageSync", periodicMessageSync)
		log.Info("WebSocket定时消息同步器已启动")
	}

	// 启动资源监控和清理
	SafeGoroutine("resourceMonitor", startResourceMonitor)
	log.Info("WebSocket资源监控器已启动")

	// 启动性能统计记录
	SafeGoroutine("performanceLogger", startPerformanceLogger)
	log.Info("WebSocket性能统计记录器已启动")
	server := &http.Server{
		Addr:         ":" + wsPort,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	if err := server.ListenAndServe(); err != nil {
		log.Errorf("WebSocket服务启动失败 (端口: %s): %v", wsPort, err)
	}
}

// ==================== 初始化函数 ====================

// init 包初始化函数
func init() {
	// 设置日志级别
	if level := beego.AppConfig.String("loglevel"); level != "" {
		if logLevel, err := log.ParseLevel(level); err == nil {
			log.SetLevel(logLevel)
		}
	}
}

// Shutdown 优雅关闭WebSocket服务
func Shutdown() {
	if connManager == nil {
		return
	}

	log.Info("开始关闭WebSocket服务")

	// 发送停止信号
	connManager.cancel()

	// 关闭所有连接
	connManager.clients.Range(func(key, value interface{}) bool {
		if wxid, ok := key.(string); ok {
			if conn, ok := value.(*websocket.Conn); ok {
				log.Debugf("关闭WebSocket连接: %s", wxid)
				safeCloseConnection(conn, wxid)
			}
		}
		return true
	})

	// 停止工作池
	if connManager.messagePool != nil {
		connManager.messagePool.Stop()
	}

	log.Info("WebSocket服务已关闭")
}

// ==================== HTTP 处理函数 ====================

// handleHealthCheck 健康检查接口
func handleHealthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	var activeConnections int64
	if connManager != nil {
		activeConnections = connManager.GetConnectionCount()
	}

	health := map[string]interface{}{
		"status":      "healthy",
		"timestamp":   time.Now().Unix(),
		"connections": activeConnections,
		"uptime":      time.Since(time.Unix(0, 0)).Seconds(),
	}

	json.NewEncoder(w).Encode(health)
}

// handleStatsCheck 统计信息接口
func handleStatsCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	var activeConnections int64
	var clientList []string

	if connManager != nil {
		activeConnections = connManager.GetConnectionCount()
		clientList = connManager.GetAllClients()
	}

	stats := map[string]interface{}{
		"active_connections": activeConnections,
		"total_connections":  atomic.LoadInt64(&connManager.connectionCount),
		"total_messages":     atomic.LoadInt64(&connManager.totalMessages),
		"total_errors":       atomic.LoadInt64(&connManager.totalErrors),
		"connected_clients":  clientList,
		"timestamp":          time.Now().Unix(),
	}

	json.NewEncoder(w).Encode(stats)
}

// ==================== 资源管理函数 ====================

// startResourceMonitor 启动资源监控器
func startResourceMonitor() {
	if connManager == nil {
		return
	}

	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			logResourceStats()
		case <-connManager.shutdownChan:
			log.Info("资源监控器正在关闭")
			return
		case <-connManager.ctx.Done():
			log.Info("资源监控器收到停止信号")
			return
		}
	}
}

// startPerformanceLogger 启动性能统计记录器
func startPerformanceLogger() {
	if connManager == nil {
		return
	}

	ticker := time.NewTicker(1 * time.Minute) // 每分钟记录一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			logPerformanceStats()
		case <-connManager.ctx.Done():
			log.Info("性能统计记录器收到停止信号")
			return
		}
	}
}

// logPerformanceStats 记录性能统计
func logPerformanceStats() {
	if connManager == nil {
		return
	}

	activeConnections := connManager.GetConnectionCount()

	log.WithFields(log.Fields{
		"active_connections": activeConnections,
		"total_connections":  atomic.LoadInt64(&connManager.connectionCount),
		"total_messages":     atomic.LoadInt64(&connManager.totalMessages),
		"total_errors":       atomic.LoadInt64(&connManager.totalErrors),
	}).Info("WebSocket性能统计")
}

// logResourceStats 记录资源统计信息
func logResourceStats() {
	if connManager == nil {
		return
	}

	activeConnections := connManager.GetConnectionCount()

	log.WithFields(log.Fields{
		"active_connections": activeConnections,
		"total_connections":  atomic.LoadInt64(&connManager.connectionCount),
		"total_messages":     atomic.LoadInt64(&connManager.totalMessages),
		"total_errors":       atomic.LoadInt64(&connManager.totalErrors),
	}).Debug("WebSocket资源统计")
}

// updateClientActivity 更新客户端活动时间
func updateClientActivity(wxid string) {
	if connManager == nil {
		return
	}

	if metadataInterface, exists := connManager.clientMetadata.Load(wxid); exists {
		if metadata, ok := metadataInterface.(*ClientMetadata); ok {
			metadata.LastActivity = time.Now()
			atomic.AddInt64(&metadata.MessageCount, 1)
			atomic.AddInt64(&connManager.totalMessages, 1)
		}
	}
}

// incrementErrorCount 增加错误计数
func incrementErrorCount(wxid string) {
	if connManager == nil {
		return
	}

	atomic.AddInt64(&connManager.totalErrors, 1)

	if metadataInterface, exists := connManager.clientMetadata.Load(wxid); exists {
		if metadata, ok := metadataInterface.(*ClientMetadata); ok {
			atomic.AddInt64(&metadata.ErrorCount, 1)
		}
	}
}

// ==================== 定时任务函数 ====================

// periodicMessageSender 定时测试消息发送器（仅用于测试）
func periodicMessageSender() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			sendTestMessagesToAllClients()
		case <-connManager.ctx.Done():
			log.Info("定时测试消息发送器收到停止信号")
			return
		}
	}
}

// sendTestMessagesToAllClients 向所有客户端发送测试消息
func sendTestMessagesToAllClients() {
	if connManager == nil {
		return
	}

	clientList := connManager.GetAllClients()
	if len(clientList) == 0 {
		return
	}

	// 构造测试消息
	testMessage := map[string]interface{}{
		"type":      "test_periodic",
		"message":   "定时测试消息",
		"timestamp": time.Now().Unix(),
		"data": map[string][]string{
			"key1": {"value1", "value2"},
			"key2": {"value3"},
		},
	}

	// 使用工作池发送消息
	for _, wxid := range clientList {
		wxidCopy := wxid // 避免闭包问题
		connManager.messagePool.Submit(func() {
			if err := sendMessageToClient(wxidCopy, testMessage); err != nil {
				log.Errorf("发送测试消息失败: wxid=%s, error=%v", wxidCopy, err)
				incrementErrorCount(wxidCopy)
			} else {
				log.Debugf("测试消息已发送: wxid=%s", wxidCopy)
			}
		})
	}
}

// ==================== WebSocket 处理函数 ====================

// handleWebSocket 处理WebSocket连接（改进错误处理）
func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	// 验证并获取wxid参数
	wxid := r.URL.Query().Get("wxid")
	if wxid == "" {
		wsErr := newWebSocketError(ErrorTypeAuth, "wxid参数是必需的", "", nil)
		logError(wsErr)
		http.Error(w, wsErr.Message, http.StatusBadRequest)
		return
	}

	// 检查连接管理器是否初始化
	if connManager == nil {
		wsErr := newWebSocketError(ErrorTypeInternal, "连接管理器未初始化", wxid, nil)
		logError(wsErr)
		http.Error(w, "服务器内部错误", http.StatusInternalServerError)
		return
	}

	// 检查连接数限制
	if connManager.GetConnectionCount() >= int64(connManager.maxConnections) {
		wsErr := newWebSocketError(ErrorTypeConnection, "连接数已达上限", wxid, nil)
		logError(wsErr)
		http.Error(w, "连接数已达上限", http.StatusServiceUnavailable)
		return
	}

	// 升级HTTP连接为WebSocket连接
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		wsErr := newWebSocketError(ErrorTypeConnection, "WebSocket升级失败", wxid, err)
		logError(wsErr)
		incrementErrorCount(wxid)
		return
	}
	defer safeCloseConnection(conn, wxid)

	// 注册新客户端连接
	if err := connManager.RegisterClient(wxid, conn, r); err != nil {
		wsErr := newWebSocketError(ErrorTypeInternal, "注册客户端失败", wxid, err)
		logError(wsErr)
		incrementErrorCount(wxid)
		return
	}
	defer connManager.UnregisterClient(wxid)

	// 配置WebSocket连接参数
	if err := configureConnection(conn); err != nil {
		wsErr := newWebSocketError(ErrorTypeConnection, "配置连接参数失败", wxid, err)
		logError(wsErr)
		incrementErrorCount(wxid)
		return
	}



	// 设置连接处理器
	setupConnectionHandlers(conn, wxid)

	// 客户端不发送心跳，禁用服务器端心跳检测
	// SafeGoroutine("heartbeat-"+wxid, func() {
	//     startHeartbeat(conn, wxid)
	// })



	// 处理消息循环（带错误恢复）
	handleMessageLoopWithRecovery(conn, wxid)


}

// ==================== 错误处理辅助函数 ====================

// logError 统一的错误日志记录
func logError(err *WebSocketError) {
	log.WithFields(log.Fields{
		"type":  err.Type,
		"wxid":  err.Wxid,
		"error": err.Err,
	}).Error(err.Message)
}

// logWarning 统一的警告日志记录
func logWarning(message string, wxid string) {
	log.WithField("wxid", wxid).Warn(message)
}

// logInfo 统一的信息日志记录
func logInfo(message string, wxid string) {
	log.WithField("wxid", wxid).Info(message)
}

// safeCloseConnection 安全关闭连接
func safeCloseConnection(conn *websocket.Conn, wxid string) {
	if conn == nil {
		return
	}

	// 发送关闭消息
	conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))

	// 设置关闭超时
	conn.SetWriteDeadline(time.Now().Add(time.Second))

	if err := conn.Close(); err != nil {
		wsErr := newWebSocketError(ErrorTypeConnection, "关闭连接失败", wxid, err)
		logError(wsErr)
	}
}

// handleMessageLoopWithRecovery 带错误恢复的消息循环处理
func handleMessageLoopWithRecovery(conn *websocket.Conn, wxid string) {
	defer func() {
		if r := recover(); r != nil {
			wsErr := newWebSocketError(ErrorTypeInternal, "消息处理发生panic", wxid, fmt.Errorf("%v", r))
			logError(wsErr)
		}
	}()

	handleMessageLoop(conn, wxid)
}

// ==================== WebSocket 连接管理辅助函数 ====================

// 这些函数已经移动到ConnectionManager中，保留空实现以维持向后兼容性
func registerClient(wxid string, conn *websocket.Conn, r *http.Request) error {
	return connManager.RegisterClient(wxid, conn, r)
}

func unregisterClient(wxid string) {
	connManager.UnregisterClient(wxid)
}

// configureConnection 配置WebSocket连接参数（改进错误处理）
func configureConnection(conn *websocket.Conn) error {
	conn.SetReadLimit(MaxReadLimit)

	// 设置长时间读取超时，适合不发送心跳的客户端
	if err := conn.SetReadDeadline(time.Now().Add(ReadTimeout)); err != nil {
		return fmt.Errorf("设置读取超时失败: %w", err)
	}

	if err := conn.SetWriteDeadline(time.Now().Add(WriteTimeout)); err != nil {
		return fmt.Errorf("设置写入超时失败: %w", err)
	}

	return nil
}

// setupConnectionHandlers 设置连接处理器
func setupConnectionHandlers(conn *websocket.Conn, wxid string) {
	// 设置Close处理器
	conn.SetCloseHandler(func(code int, text string) error {
		log.WithFields(log.Fields{
			"wxid": wxid,
			"code": code,
			"text": text,
		}).Info("客户端正在关闭连接")
		return nil
	})
}

// startHeartbeat 启动心跳检测（优化资源管理）
func startHeartbeat(conn *websocket.Conn, wxid string) {
	ticker := time.NewTicker(HeartbeatInterval)
	defer ticker.Stop()

	// 用于检测pong响应的通道
	pongReceived := make(chan bool, 1)

	// 设置pong处理器
	conn.SetPongHandler(func(string) error {
		select {
		case pongReceived <- true:
		default:
		}
		conn.SetReadDeadline(time.Now().Add(ReadTimeout))
		updateHeartbeatTime(wxid)
		return nil
	})

	for {
		select {
		case <-ticker.C:
			// 检查连接是否仍然存在
			if _, exists := connManager.GetClient(wxid); !exists {
				// 连接已被移除，停止心跳
				log.Debugf("连接已移除，停止心跳: wxid=%s", wxid)
				return
			}

			// 发送心跳
			if err := sendHeartbeat(conn, wxid); err != nil {
				log.Debugf("心跳检测失败: wxid=%s, error=%v", wxid, err)
				incrementErrorCount(wxid)
				return
			}

			// 等待pong响应，设置超时
			select {
			case <-pongReceived:
				// 收到pong响应，连接正常
				log.Debugf("心跳正常: wxid=%s", wxid)
			case <-time.After(PingTimeout):
				// pong响应超时，认为连接已断开
				log.Warnf("心跳超时，关闭连接: wxid=%s", wxid)
				incrementErrorCount(wxid)
				conn.Close()
				return
			}

		case <-connManager.ctx.Done():
			log.Debugf("收到停止信号，停止心跳: wxid=%s", wxid)
			return
		}
	}
}

// sendHeartbeat 发送心跳包
func sendHeartbeat(conn *websocket.Conn, wxid string) error {
	if err := conn.SetWriteDeadline(time.Now().Add(WriteTimeout)); err != nil {
		return fmt.Errorf("设置心跳写入超时失败: %w", err)
	}

	if err := conn.WriteMessage(websocket.PingMessage, []byte("heartbeat")); err != nil {
		return fmt.Errorf("发送心跳失败: %w", err)
	}

	return nil
}

// updateHeartbeatTime 更新心跳时间
func updateHeartbeatTime(wxid string) {
	if metadataInterface, exists := connManager.clientMetadata.Load(wxid); exists {
		if metadata, ok := metadataInterface.(*ClientMetadata); ok {
			metadata.LastHeartbeat = time.Now()
		}
	}
}

// handleMessageLoop 处理消息循环
func handleMessageLoop(conn *websocket.Conn, wxid string) {
	// 设置初始读取超时
	conn.SetReadDeadline(time.Now().Add(ReadTimeout))

	for {
		select {
		case <-connManager.ctx.Done():
			log.Debugf("收到停止信号，退出消息循环: wxid=%s", wxid)
			return
		default:
			// 继续处理消息
		}

		messageType, data, err := conn.ReadMessage()
		if err != nil {
			handleConnectionError(err, wxid)
			break
		}

		// 重置读取超时
		conn.SetReadDeadline(time.Now().Add(ReadTimeout))

		// 使用工作池处理消息，避免创建过多goroutine
		dataCopy := make([]byte, len(data)) // 复制数据避免竞态条件
		copy(dataCopy, data)

		connManager.messagePool.Submit(func() {
			handleIncomingMessage(messageType, dataCopy, conn, wxid)
		})

		// 更新客户端活动时间
		updateClientActivity(wxid)
	}
}

// handleConnectionError 处理连接错误
func handleConnectionError(err error, wxid string) {
	if websocket.IsCloseError(err, websocket.CloseGoingAway, websocket.CloseNormalClosure, websocket.CloseNoStatusReceived) {
		log.WithField("wxid", wxid).Info("客户端正常断开")
	} else if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
		log.WithFields(log.Fields{
			"wxid":  wxid,
			"error": err,
		}).Error("WebSocket连接意外断开")
		incrementErrorCount(wxid)
	} else {
		log.WithFields(log.Fields{
			"wxid":  wxid,
			"error": err,
		}).Error("连接读取错误")
		incrementErrorCount(wxid)
	}
}

// handleIncomingMessage 处理接收到的消息
func handleIncomingMessage(messageType int, data []byte, conn *websocket.Conn, wxid string) {
	switch messageType {
	case websocket.TextMessage:
		handleTextMessage(data, wxid)
	case websocket.BinaryMessage:
		handleBinaryMessage(data, wxid)
	case websocket.PingMessage:
		handlePingMessage(conn)
	case websocket.PongMessage:
		// Pong消息，连接正常，无需处理
	default:
		fmt.Printf("[WARN] 收到未知消息类型 %d: wxid=%s\n", messageType, wxid)
	}
}

// handleTextMessage 处理文本消息
func handleTextMessage(data []byte, wxid string) {
	if len(data) == 0 {
		return
	}

	// 更新客户端活动时间
	updateClientActivity(wxid)

	// 尝试解析JSON
	var jsonMessage interface{}
	if err := json.Unmarshal(data, &jsonMessage); err != nil {
		// 过滤掉心跳消息的日志
		message := string(data)
		if message != "ping" && message != "heartbeat" {
			fmt.Printf("[DEBUG] 收到非JSON文本消息: wxid=%s, message=%s\n", wxid, message)
		}
	}
}

// handleBinaryMessage 处理二进制消息
func handleBinaryMessage(data []byte, wxid string) {
	// 只有在数据长度异常时才输出日志
	if len(data) > 1024 {
		fmt.Printf("[DEBUG] 收到大型二进制消息: wxid=%s, length=%d\n", wxid, len(data))
	}
}

// handlePingMessage 处理Ping消息
func handlePingMessage(conn *websocket.Conn) {
	conn.SetWriteDeadline(time.Now().Add(PingTimeout))
	conn.WriteMessage(websocket.PongMessage, nil)
}

// ==================== 消息发送函数 ====================

// sendMessageToWxid 向指定wxid发送消息
func sendMessageToWxid(wxid string, data []mm.AddMsg) error {
	return sendMessageToClient(wxid, data)
}

// sendMessageToClient 向指定客户端发送消息（改进错误处理）
func sendMessageToClient(wxid string, message interface{}) error {
	conn, exists := connManager.GetClient(wxid)
	if !exists {
		return newWebSocketError(ErrorTypeConnection, "客户端未找到", wxid, nil)
	}

	// 使用带超时的发送
	return sendMessageWithTimeout(conn, wxid, message)
}

// sendMessageWithTimeout 带超时的消息发送
func sendMessageWithTimeout(conn *websocket.Conn, wxid string, message interface{}) error {
	// 创建发送完成通道
	done := make(chan error, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				done <- newWebSocketError(ErrorTypeInternal, fmt.Sprintf("发送消息时panic: %v", r), wxid, nil)
			}
		}()

		// 设置写入超时
		if err := conn.SetWriteDeadline(time.Now().Add(WriteTimeout)); err != nil {
			done <- newWebSocketError(ErrorTypeConnection, "设置写入超时失败", wxid, err)
			return
		}

		// 发送消息
		if err := conn.WriteJSON(message); err != nil {
			done <- newWebSocketError(ErrorTypeMessage, "发送消息失败", wxid, err)
			return
		}

		done <- nil
	}()

	// 等待发送完成或超时
	select {
	case err := <-done:
		if err != nil {
			incrementErrorCount(wxid)
			logError(err.(*WebSocketError))
			// 如果发送失败，可能连接已断开，从客户端列表中移除
			connManager.UnregisterClient(wxid)
			return err
		}
		// 更新客户端活动时间
		updateClientActivity(wxid)
		return nil
	case <-time.After(WriteTimeout + time.Second):
		incrementErrorCount(wxid)
		wsErr := newWebSocketError(ErrorTypeConnection, "发送消息超时", wxid, nil)
		logError(wsErr)
		connManager.UnregisterClient(wxid)
		return wsErr
	}
}

// SendMessageToAllClients 向所有连接的客户端广播消息
//
// 此函数会向当前所有活跃的WebSocket连接发送相同的消息。
// 使用工作池模式避免创建过多goroutine。
//
// 参数：
//   - message: 要发送的消息，可以是任何可序列化为JSON的数据结构
//
// 行为：
//   - 如果某个客户端发送失败，会自动从连接列表中移除该客户端
//   - 发送过程是异步的，不会阻塞调用者
//   - 会记录每个客户端的发送结果到日志
//
// 使用示例：
//
//	message := map[string]interface{}{
//	    "type": "broadcast",
//	    "data": "系统通知消息",
//	}
//	SendMessageToAllClients(message)
func SendMessageToAllClients(message interface{}) {
	if connManager == nil {
		return
	}

	// 获取所有客户端列表
	clientList := connManager.GetAllClients()
	if len(clientList) == 0 {
		return
	}

	// 使用工作池发送消息
	for _, wxid := range clientList {
		wxidCopy := wxid // 避免闭包问题
		connManager.messagePool.Submit(func() {
			if err := sendMessageToClient(wxidCopy, message); err != nil {
				log.Errorf("广播消息失败: wxid=%s, error=%v", wxidCopy, err)
			} else {
				log.Debugf("广播消息成功: wxid=%s", wxidCopy)
			}
		})
	}
}

// SendTestMessage 发送测试消息到指定客户端
func SendTestMessage(wxid string) error {
	testMessage := map[string]interface{}{
		"type":      "test",
		"message":   "来自WebSocket服务器的测试消息",
		"timestamp": time.Now().Unix(),
		"wxid":      wxid,
	}

	if err := sendMessageToClient(wxid, testMessage); err != nil {
		return fmt.Errorf("发送测试消息失败: %w", err)
	}

	fmt.Printf("[SUCCESS] 测试消息已发送: wxid=%s\n", wxid)
	return nil
}

// GetConnectedClients 获取所有连接的客户端列表
func GetConnectedClients() []string {
	if connManager == nil {
		return nil
	}
	return connManager.GetAllClients()
}

// SendWeChatMessagesToWebSocket 将微信消息推送到WebSocket客户端
//
// 此函数是微信消息推送的核心功能，负责：
// 1. 格式化微信原始消息为前端友好的格式
// 2. 触发自动功能处理（红包领取、收款确认等）
// 3. 通过WebSocket实时推送到对应的客户端
//
// 参数：
//   - wxid: 目标微信账号ID
//   - messages: 微信消息列表，来自微信DLL的原始消息数据
//
// 返回值：
//   - error: 如果推送失败返回错误，成功返回nil
//
// 功能特性：
//   - 自动解析各种消息类型（文本、图片、语音、视频、链接等）
//   - 提取消息的详细信息（发送者、接收者、时间戳等）
//   - 异步处理自动功能（不阻塞消息推送）
//   - 自动处理连接失败的情况
//
// 消息格式：
//
//	推送的消息包含以下字段：
//	- type: "wechat_message"
//	- wxid: 微信账号ID
//	- timestamp: 推送时间戳
//	- count: 消息数量
//	- messages: 格式化后的消息列表
//
// 使用示例：
//
//	err := SendWeChatMessagesToWebSocket("wxid_example", messageList)
//	if err != nil {
//	    log.Printf("推送消息失败: %v", err)
//	}
func SendWeChatMessagesToWebSocket(wxid string, messages []mm.AddMsg) error {
	if len(messages) == 0 {
		return nil
	}

	conn, exists := connManager.GetClient(wxid)
	if !exists {
		return fmt.Errorf("client not found for wxid=%s", wxid)
	}

	// 格式化消息
	formattedMessages := formatWeChatMessages(messages)

	// 检查并处理红包消息和收款消息
	for _, msg := range formattedMessages {
		processAutoFeatures(wxid, msg)
	}

	// 构造推送消息
	pushMessage := map[string]interface{}{
		"type":      "wechat_message",
		"wxid":      wxid,
		"timestamp": time.Now().Unix(),
		"count":     len(messages),
		"messages":  formattedMessages,
	}

	// 使用优化的发送函数
	if err := sendMessageWithTimeout(conn, wxid, pushMessage); err != nil {
		log.Errorf("推送微信消息到WebSocket失败: wxid=%s, error=%v", wxid, err)
		return fmt.Errorf("failed to send wechat messages to wxid=%s: %w", wxid, err)
	}

	log.Infof("成功推送 %d 条微信消息到WebSocket: wxid=%s", len(messages), wxid)
	return nil
}

// formatWeChatMessages 格式化微信消息为易读格式
func formatWeChatMessages(messages []mm.AddMsg) []map[string]interface{} {
	var formattedMessages []map[string]interface{}

	for i := range messages {
		msg := &messages[i]

		// 输出完全未处理的原始数据
		log.WithField("rawMessage", msg).Info("原始数据")

		// 基础信息
		msgId := msg.GetMsgId()
		fromUser := cleanString(msg.GetFromUserName().GetString_())
		toUser := cleanString(msg.GetToUserName().GetString_())
		content := cleanString(msg.GetContent().GetString_())
		msgType := uint32(msg.GetMsgType())
		createTime := msg.GetCreateTime()

		// 解析和清理消息内容
		parsedContent := parseMessageContent(content, msgType)

		// 获取用户昵称（如果可能）
		fromUserNickname := extractNicknameFromWxid(fromUser)
		toUserNickname := extractNicknameFromWxid(toUser)

		// 检查是否为群聊消息并提取发送者信息
		isGroup := isGroupMessage(fromUser, toUser)
		actualSender := fromUser
		actualSenderName := fromUserNickname

		if isGroup {
			if extraData := parsedContent["extra"]; extraData != nil {
				if extra, ok := extraData.(map[string]interface{}); ok {
					if senderWxid, exists := extra["senderWxid"]; exists && senderWxid != nil {
						actualSender = senderWxid.(string)
					}
					if senderNickname, exists := extra["senderNickname"]; exists && senderNickname != nil {
						actualSenderName = senderNickname.(string)
					}
				}
			}
		}

		formatted := map[string]interface{}{
			"msgId":           msgId,
			"fromUser":        fromUser,
			"fromUserName":    fromUserNickname,
			"toUser":          toUser,
			"toUserName":      toUserNickname,
			"content":         parsedContent["content"],
			"contentType":     parsedContent["type"],
			"originalContent": content, // 保留原始内容用于调试
			"msgType":         msgType,
			"msgTypeDesc":     getMsgTypeDescription(msgType),
			"createTime":      createTime,
			"createTimeStr":   formatTimestamp(createTime),
			"isGroupMessage":  isGroup,
			// 群聊消息的实际发送者信息
			"actualSender":     actualSender,
			"actualSenderName": actualSenderName,
		}

		// 添加特殊消息类型的解析数据
		if extraData := parsedContent["extra"]; extraData != nil {
			formatted["extraData"] = extraData
		}

		formattedMessages = append(formattedMessages, formatted)
	}

	return formattedMessages
}

// cleanString 清理字符串，移除不可见字符
func cleanString(s string) string {
	if s == "" {
		return s
	}
	// 移除字符串前后的空白字符
	return strings.TrimSpace(s)
}

// 全局消息解析器实例
var globalMessageParser = NewEnhancedMessageParser(true, false)

// parseMessageContent 解析消息内容（使用增强解析器）
func parseMessageContent(content string, msgType uint32) map[string]interface{} {
	// 使用增强消息解析器
	parsed := globalMessageParser.ParseMessage(content, msgType)

	result := map[string]interface{}{
		"content": parsed.Content,
		"type":    parsed.ContentType,
	}

	// 如果有额外数据，添加到结果中
	if len(parsed.ExtraData) > 0 {
		result["extra"] = parsed.ExtraData
	}

	// 添加解析元信息
	result["parseInfo"] = map[string]interface{}{
		"category": parsed.Category,
		"isSystem": parsed.IsSystem,
		"parsedAt": parsed.ParsedAt,
	}

	return result
}

// getMsgTypeDescription 获取消息类型描述（使用增强解析器）
func getMsgTypeDescription(msgType uint32) string {
	return globalMessageParser.GetMessageTypeDescription(msgType)
}

// formatTimestamp 格式化时间戳
func formatTimestamp(timestamp uint32) string {
	if timestamp == 0 {
		return ""
	}
	t := time.Unix(int64(timestamp), 0)
	return t.Format("2006-01-02 15:04:05")
}

// isGroupMessage 判断是否为群消息
func isGroupMessage(fromUser, toUser string) bool {
	// 群聊的wxid通常包含@chatroom
	return strings.Contains(fromUser, "@chatroom") || strings.Contains(toUser, "@chatroom")
}

// extractNicknameFromWxid 从wxid提取昵称（简单实现）
func extractNicknameFromWxid(wxid string) string {
	// 这里可以实现更复杂的昵称获取逻辑
	// 目前只是简单处理
	if wxid == "" {
		return ""
	}

	// 如果是群聊
	if strings.Contains(wxid, "@chatroom") {
		return "[群聊]"
	}

	// 如果是公众号
	if strings.HasPrefix(wxid, "gh_") {
		return "[公众号]"
	}

	// 普通用户，返回wxid（后续可以通过API获取真实昵称）
	return wxid
}

// parseImageMessage 解析图片消息
func parseImageMessage(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{
		"type": "image",
	}

	// 解析XML内容
	xmlData := parseXMLToMap(content)
	if xmlData != nil {
		result["xmlData"] = xmlData

		// 提取常用字段
		if img, ok := xmlData["img"].(map[string]interface{}); ok {
			if cdnthumburl, exists := img["cdnthumburl"]; exists {
				result["thumbUrl"] = cdnthumburl
			}
			if cdnmidimgurl, exists := img["cdnmidimgurl"]; exists {
				result["midImgUrl"] = cdnmidimgurl
			}
			if cdnbigimgurl, exists := img["cdnbigimgurl"]; exists {
				result["bigImgUrl"] = cdnbigimgurl
			}
			if length, exists := img["length"]; exists {
				result["fileSize"] = length
			}
		}
	}

	return result
}

// parseVoiceMessage 解析语音消息
func parseVoiceMessage(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{
		"type": "voice",
	}

	// 解析XML内容
	xmlData := parseXMLToMap(content)
	if xmlData != nil {
		result["xmlData"] = xmlData

		// 提取语音相关信息
		if voicemsg, ok := xmlData["voicemsg"].(map[string]interface{}); ok {
			if voicelength, exists := voicemsg["voicelength"]; exists {
				result["duration"] = voicelength
			}
			if clientmsgid, exists := voicemsg["clientmsgid"]; exists {
				result["clientMsgId"] = clientmsgid
			}
		}
	}

	return result
}

// parseLocationMessage 解析位置消息
func parseLocationMessage(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{
		"type": "location",
	}

	// 解析XML内容
	xmlData := parseXMLToMap(content)
	if xmlData != nil {
		result["xmlData"] = xmlData

		// 提取位置信息
		if location, ok := xmlData["location"].(map[string]interface{}); ok {
			if x, exists := location["x"]; exists {
				result["longitude"] = x
			}
			if y, exists := location["y"]; exists {
				result["latitude"] = y
			}
			if label, exists := location["label"]; exists {
				result["address"] = label
			}
			if poiname, exists := location["poiname"]; exists {
				result["poiName"] = poiname
			}
		}
	}

	return result
}

// parseLinkMessage 解析链接/小程序消息（优化版本，避免解析过多XML标签）
func parseLinkMessage(content string) map[string]interface{} {
	if !strings.Contains(content, "<msg>") {
		return nil
	}

	result := map[string]interface{}{
		"type": "link",
	}

	// 使用轻量级解析器，只提取关键信息
	linkInfo := parseLinkMessageLightweight(content)
	if linkInfo != nil {
		// 合并解析结果
		for key, value := range linkInfo {
			result[key] = value
		}
	}

	return result
}

// parseLinkMessageLightweight 轻量级链接/小程序消息解析器
// 只提取关键信息，避免解析复杂的XML结构
func parseLinkMessageLightweight(content string) map[string]interface{} {
	result := make(map[string]interface{})

	// 提取标题
	if title := extractXMLValue(content, "title"); title != "" {
		result["title"] = cleanString(title)
	}

	// 提取描述
	if desc := extractXMLValue(content, "des"); desc != "" {
		result["description"] = cleanString(desc)
	}

	// 提取URL
	if url := extractXMLValue(content, "url"); url != "" {
		result["url"] = url
	}

	// 检查是否为小程序（优先从weappinfo节点查找appid）
	appid := extractFromWeappInfoNode(content, "appid")
	if appid == "" {
		// 如果weappinfo中没有，尝试从appmsg中查找
		appid = extractXMLValue(content, "appid")
	}

	if appid != "" {
		result["appId"] = appid
		result["type"] = "miniprogram"

		// 提取小程序特有信息（只提取关键字段，避免过多XML解析）
		if weappInfo := extractWeappInfo(content); weappInfo != nil {
			result["weappInfo"] = weappInfo
		}

		// 如果没有标题，设置默认标题
		if result["title"] == nil || result["title"] == "" {
			result["title"] = "[小程序]"
		}
	} else if fileext := extractXMLValue(content, "fileext"); fileext != "" {
		// 文件类型
		result["fileExt"] = fileext
		result["type"] = "file"
		if result["title"] == nil || result["title"] == "" {
			result["title"] = "[文件]"
		}
	} else {
		// 普通链接
		result["type"] = "link"
		if result["title"] == nil || result["title"] == "" {
			result["title"] = "[链接]"
		}
	}

	// 提取缩略图信息（如果存在）
	if thumbInfo := extractThumbnailInfo(content); thumbInfo != nil {
		result["thumbnail"] = thumbInfo
	}

	return result
}

// extractXMLValue 提取XML中指定标签的值（优化版本）
func extractXMLValue(content, tagName string) string {
	// 方法1: 标准的开始和结束标签
	pattern := fmt.Sprintf(`<%s[^>]*>([^<]*)</%s>`, tagName, tagName)
	re := regexp.MustCompile(pattern)
	matches := re.FindStringSubmatch(content)
	if len(matches) > 1 {
		value := strings.TrimSpace(matches[1])
		if value != "" {
			return value
		}
	}

	// 方法2: 处理包含CDATA的情况
	pattern = fmt.Sprintf(`<%s[^>]*><!\[CDATA\[(.*?)\]\]></%s>`, tagName, tagName)
	re = regexp.MustCompile(pattern)
	matches = re.FindStringSubmatch(content)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	// 方法3: 处理自闭合标签或属性值
	pattern = fmt.Sprintf(`<%s[^>]*>([^<]+)`, tagName)
	re = regexp.MustCompile(pattern)
	matches = re.FindStringSubmatch(content)
	if len(matches) > 1 {
		value := strings.TrimSpace(matches[1])
		// 过滤掉明显是其他标签的内容
		if !strings.Contains(value, "<") {
			return value
		}
	}

	// 方法4: 尝试从属性中提取（某些情况下值可能在属性中）
	pattern = fmt.Sprintf(`<%s[^>]*\s%s\s*=\s*["']([^"']*)["']`, tagName, tagName)
	re = regexp.MustCompile(pattern)
	matches = re.FindStringSubmatch(content)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	return ""
}

// extractWeappInfo 提取小程序特有信息（优化版本）
func extractWeappInfo(content string) map[string]interface{} {
	weappInfo := make(map[string]interface{})

	// 提取小程序用户名（从weappinfo节点）
	if username := extractFromWeappInfoNode(content, "username"); username != "" {
		weappInfo["username"] = username
	}

	// 提取页面路径
	if pagepath := extractFromWeappInfoNode(content, "pagepath"); pagepath != "" {
		weappInfo["pagepath"] = pagepath
	}

	// 提取版本号
	if version := extractFromWeappInfoNode(content, "version"); version != "" {
		weappInfo["version"] = version
	}

	// 提取小程序ID
	if appid := extractFromWeappInfoNode(content, "appid"); appid != "" {
		weappInfo["appid"] = appid
	}

	// 提取显示名称
	if displayName := extractXMLValue(content, "sourcedisplayname"); displayName != "" {
		weappInfo["displayName"] = displayName
	}

	// 提取小程序图标
	if iconUrl := extractFromWeappInfoNode(content, "weappiconurl"); iconUrl != "" {
		weappInfo["iconUrl"] = iconUrl
	}

	// 提取小程序页面缩略图
	if pageThumb := extractFromWeappInfoNode(content, "weapppagethumbrawurl"); pageThumb != "" {
		weappInfo["pageThumbUrl"] = pageThumb
	}

	if len(weappInfo) == 0 {
		return nil
	}

	return weappInfo
}

// extractFromWeappInfoNode 从weappinfo节点中提取特定字段
func extractFromWeappInfoNode(content, fieldName string) string {
	// 首先找到weappinfo节点
	weappInfoPattern := `<weappinfo>(.*?)</weappinfo>`
	weappInfoRe := regexp.MustCompile(weappInfoPattern)
	weappInfoMatches := weappInfoRe.FindStringSubmatch(content)

	if len(weappInfoMatches) > 1 {
		weappInfoContent := weappInfoMatches[1]
		// 在weappinfo节点内查找指定字段
		return extractXMLValue(weappInfoContent, fieldName)
	}

	// 如果weappinfo节点不存在，尝试直接从整个内容中提取
	return extractXMLValue(content, fieldName)
}

// extractThumbnailInfo 提取缩略图信息
func extractThumbnailInfo(content string) map[string]interface{} {
	thumbInfo := make(map[string]interface{})

	// 提取CDN缩略图URL
	if cdnThumbUrl := extractXMLValue(content, "cdnthumburl"); cdnThumbUrl != "" {
		thumbInfo["cdnThumbUrl"] = cdnThumbUrl
	}

	// 提取缩略图尺寸
	if width := extractXMLValue(content, "cdnthumbwidth"); width != "" {
		thumbInfo["width"] = width
	}

	if height := extractXMLValue(content, "cdnthumbheight"); height != "" {
		thumbInfo["height"] = height
	}

	// 提取小程序页面缩略图
	if weapppageThumb := extractXMLValue(content, "weapppagethumbrawurl"); weapppageThumb != "" {
		thumbInfo["weappPageThumb"] = weapppageThumb
	}

	if len(thumbInfo) == 0 {
		return nil
	}

	return thumbInfo
}

// ==================== 测试和调试函数 ====================

// TestParseLinkMessage 测试链接/小程序消息解析（用于调试）
func TestParseLinkMessage(xmlContent string) map[string]interface{} {
	fmt.Printf("[DEBUG] 开始解析链接/小程序消息\n")

	result := parseLinkMessage(xmlContent)

	// 输出解析结果
	if result != nil {
		fmt.Printf("[DEBUG] 解析结果:\n")
		fmt.Printf("  - 类型: %v\n", result["type"])
		fmt.Printf("  - 标题: %v\n", result["title"])
		fmt.Printf("  - 描述: %v\n", result["description"])
		fmt.Printf("  - URL: %v\n", result["url"])

		if appId, exists := result["appId"]; exists {
			fmt.Printf("  - 小程序ID: %v\n", appId)
		}

		if weappInfo, exists := result["weappInfo"]; exists {
			fmt.Printf("  - 小程序信息: %+v\n", weappInfo)
		}

		if thumbnail, exists := result["thumbnail"]; exists {
			fmt.Printf("  - 缩略图信息: %+v\n", thumbnail)
		}

		fmt.Printf("[DEBUG] 总共提取到 %d 个字段\n", len(result))
	} else {
		fmt.Printf("[DEBUG] 解析失败，返回nil\n")
	}

	return result
}

// 优化后的XML解析总结：
// 1. 使用轻量级正则表达式，避免复杂的XML树解析
// 2. 只提取关键字段，减少不必要的标签解析
// 3. 优先从weappinfo节点提取小程序信息
// 4. 支持CDATA和多种XML格式
// 5. 提供调试函数便于测试和验证

// ParseWeChatMessages 解析微信消息为结构化数据（供API使用）
func ParseWeChatMessages(messages []mm.AddMsg) []map[string]interface{} {
	return formatWeChatMessages(messages)
}

// ParseSingleWeChatMessage 解析单条微信消息
func ParseSingleWeChatMessage(msg *mm.AddMsg) map[string]interface{} {
	return formatSingleWeChatMessage(msg)
}

// formatSingleWeChatMessage 格式化单条微信消息
func formatSingleWeChatMessage(msg *mm.AddMsg) map[string]interface{} {
	// 基础信息
	msgId := msg.GetMsgId()
	fromUser := cleanString(msg.GetFromUserName().GetString_())
	toUser := cleanString(msg.GetToUserName().GetString_())
	content := cleanString(msg.GetContent().GetString_())
	msgType := uint32(msg.GetMsgType())
	createTime := msg.GetCreateTime()

	// 解析和清理消息内容
	parsedContent := parseMessageContent(content, msgType)

	// 获取用户昵称（如果可能）
	fromUserNickname := extractNicknameFromWxid(fromUser)
	toUserNickname := extractNicknameFromWxid(toUser)

	formatted := map[string]interface{}{
		"msgId":           msgId,
		"fromUser":        fromUser,
		"fromUserName":    fromUserNickname,
		"toUser":          toUser,
		"toUserName":      toUserNickname,
		"content":         parsedContent["content"],
		"contentType":     parsedContent["type"],
		"originalContent": content, // 保留原始内容用于调试
		"msgType":         msgType,
		"msgTypeDesc":     getMsgTypeDescription(msgType),
		"createTime":      createTime,
		"createTimeStr":   formatTimestamp(createTime),
		"isGroupMessage":  isGroupMessage(fromUser, toUser),
	}

	// 添加特殊消息类型的解析数据
	if extraData := parsedContent["extra"]; extraData != nil {
		formatted["extraData"] = extraData
	}

	return formatted
}

// FastXMLParser 快速XML解析器，专门用于微信消息XML
type FastXMLParser struct {
	cache map[string]map[string]interface{} // 简单的缓存机制
	mutex sync.RWMutex
}

// NewFastXMLParser 创建新的快速XML解析器
func NewFastXMLParser() *FastXMLParser {
	return &FastXMLParser{
		cache: make(map[string]map[string]interface{}),
	}
}

// 全局XML解析器实例
var globalXMLParser = NewFastXMLParser()

// parseXMLToMap 将XML字符串解析为map结构（优化版本）
func parseXMLToMap(xmlStr string) map[string]interface{} {
	if xmlStr == "" {
		return nil
	}

	// 清理XML字符串
	xmlStr = strings.TrimSpace(xmlStr)

	// 如果不包含XML标签，直接返回
	if !strings.Contains(xmlStr, "<") {
		return nil
	}

	// 检查缓存（对于相同的XML内容，避免重复解析）
	globalXMLParser.mutex.RLock()
	if cached, exists := globalXMLParser.cache[xmlStr]; exists {
		globalXMLParser.mutex.RUnlock()
		return cached
	}
	globalXMLParser.mutex.RUnlock()

	// 解析XML
	result := globalXMLParser.parseXMLFast(xmlStr)

	// 缓存结果（限制缓存大小）
	globalXMLParser.mutex.Lock()
	if len(globalXMLParser.cache) < 100 { // 限制缓存大小
		globalXMLParser.cache[xmlStr] = result
	}
	globalXMLParser.mutex.Unlock()

	return result
}

// parseXMLFast 快速解析XML（避免正则表达式）
func (p *FastXMLParser) parseXMLFast(xmlStr string) map[string]interface{} {
	result := make(map[string]interface{})

	// 简单的状态机解析，比正则表达式更高效
	i := 0
	for i < len(xmlStr) {
		// 查找标签开始
		start := strings.Index(xmlStr[i:], "<")
		if start == -1 {
			break
		}
		start += i

		// 查找标签结束
		end := strings.Index(xmlStr[start:], ">")
		if end == -1 {
			break
		}
		end += start

		// 提取标签名
		tagContent := xmlStr[start+1 : end]
		if strings.HasPrefix(tagContent, "/") || strings.HasPrefix(tagContent, "!") {
			i = end + 1
			continue
		}

		// 提取标签名（忽略属性）
		spaceIndex := strings.Index(tagContent, " ")
		var tagName string
		if spaceIndex != -1 {
			tagName = tagContent[:spaceIndex]
		} else {
			tagName = tagContent
		}

		// 查找对应的结束标签
		closeTag := "</" + tagName + ">"
		closeIndex := strings.Index(xmlStr[end+1:], closeTag)
		if closeIndex == -1 {
			i = end + 1
			continue
		}
		closeIndex += end + 1

		// 提取标签内容
		content := xmlStr[end+1 : closeIndex]

		// 如果内容包含子标签，递归解析
		if strings.Contains(content, "<") {
			result[tagName] = p.parseXMLFast(content)
		} else {
			result[tagName] = strings.TrimSpace(content)
		}

		i = closeIndex + len(closeTag)
	}

	return result
}

// parseXMLContent 递归解析XML内容
func parseXMLContent(content string) interface{} {
	if content == "" {
		return ""
	}

	// 查找所有子元素 - 使用更简单的正则表达式
	elementRegex := regexp.MustCompile(`<(\w+)(?:\s+[^>]*)?>([^<]*)</(\w+)>`)
	matches := elementRegex.FindAllStringSubmatch(content, -1)

	if len(matches) == 0 {
		// 没有子元素，返回文本内容
		return strings.TrimSpace(content)
	}

	result := make(map[string]interface{})

	for _, match := range matches {
		if len(match) >= 4 {
			tagName := match[1]
			tagContent := match[2]
			closeTag := match[3]

			// 确保开始和结束标签匹配
			if tagName == closeTag {
				// 递归解析子内容
				if strings.Contains(tagContent, "<") {
					result[tagName] = parseXMLContent(tagContent)
				} else {
					result[tagName] = strings.TrimSpace(tagContent)
				}
			}
		}
	}

	// 如果只有一个元素且没有其他内容，直接返回该元素
	if len(result) == 1 {
		for _, value := range result {
			return value
		}
	}

	return result
}

// parseXMLAttributes 解析XML属性
func parseXMLAttributes(xmlTag string) map[string]string {
	attrs := make(map[string]string)

	// 提取属性
	attrRegex := regexp.MustCompile(`(\w+)=["']([^"']*)["']`)
	matches := attrRegex.FindAllStringSubmatch(xmlTag, -1)

	for _, match := range matches {
		if len(match) >= 3 {
			attrs[match[1]] = match[2]
		}
	}

	return attrs
}

// 自动功能相关代码已移动到独立文件中：
// - AutoFeatures.go: 自动红包领取功能
// - AutoCollectMoney.go: 自动收款确认功能
// - AutoStatusManager.go: 状态管理和Redis持久化
// - AutoNotifications.go: WebSocket通知管理

// 自动功能处理函数已移动到独立文件中，这里只保留调用

// 所有自动功能代码已移动到独立文件中：
// - AutoFeatures.go: 自动红包领取功能
// - AutoCollectMoney.go: 自动收款确认功能
// - AutoStatusManager.go: 状态管理和Redis持久化
// - AutoNotifications.go: WebSocket通知管理

// 这里只保留对独立文件中函数的调用

// 在消息处理中调用自动功能
func processAutoFeatures(wxid string, msg map[string]interface{}) {
	// 预先获取所有自动功能状态，减少Redis并发访问
	redpacketEnabled := GetAutoRedPacketStatus(wxid)
	collectEnabled := GetAutoCollectMoneyStatus(wxid)
	friendEnabled := GetAutoAcceptFriendStatus(wxid)

	// 使用单个goroutine处理所有自动功能，避免过多并发
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("[AUTO_FEATURES] panic recovered: %v\n", r)
			}
		}()

		// 处理自动红包领取
		if redpacketEnabled {
			ProcessRedPacketMessageWithStatus(wxid, msg, true)
		}

		// 处理自动收款确认
		if collectEnabled {
			ProcessCollectMoneyMessageWithStatus(wxid, msg, true)
		}

		// 处理自动通过好友请求
		if friendEnabled {
			ProcessFriendRequestMessageWithStatus(wxid, msg, true)
		}
	}()
}

// IsClientConnected 检查客户端是否连接
func IsClientConnected(wxid string) bool {
	if connManager == nil {
		return false
	}
	_, exists := connManager.GetClient(wxid)
	return exists
}

// GetConnectionCount 获取当前连接数
func GetConnectionCount() int {
	if connManager == nil {
		return 0
	}
	return int(connManager.GetConnectionCount())
}

// periodicMessageSync 定时消息同步器
func periodicMessageSync() {
	if connManager == nil {
		return
	}

	ticker := time.NewTicker(SyncInterval)
	defer ticker.Stop()

	syncCount := 0
	httpClient := &http.Client{
		Timeout: 10 * time.Second, // 设置HTTP请求超时
	}

	for {
		select {
		case <-ticker.C:
			connectedClients := getConnectedClientsList()
			if len(connectedClients) == 0 {
				continue // 没有连接的客户端，跳过同步
			}

			syncCount++
			showLog := syncCount%SyncLogInterval == 1

			// 使用工作池模式限制并发数
			syncClients(connectedClients, httpClient, showLog)

		case <-connManager.ctx.Done():
			log.Info("定时消息同步器收到停止信号")
			return
		}
	}
}

// getConnectedClientsList 获取已连接客户端列表（性能优化版本）
func getConnectedClientsList() []string {
	if connManager == nil {
		return nil
	}
	return connManager.GetAllClients()
}

// syncClients 同步客户端消息（使用工作池模式）
func syncClients(clientList []string, httpClient *http.Client, showLog bool) {
	// 限制并发数，避免过多goroutine
	const maxConcurrency = 10
	semaphore := make(chan struct{}, maxConcurrency)

	for _, wxid := range clientList {
		semaphore <- struct{}{} // 获取信号量

		go func(clientWxid string) {
			defer func() { <-semaphore }() // 释放信号量

			syncSingleClient(clientWxid, httpClient, showLog)
		}(wxid)
	}
}

// syncSingleClient 同步单个客户端消息
func syncSingleClient(wxid string, httpClient *http.Client, showLog bool) {
	if showLog {
		fmt.Printf("[AUTO_SYNC] 为 %s 执行定时消息同步\n", wxid)
	}

	// 构造同步URL
	syncUrl := fmt.Sprintf("%s?wxid=%s", SyncAPIEndpoint, wxid)

	// 发送HTTP请求
	resp, err := httpClient.Get(syncUrl)
	if err != nil {
		fmt.Printf("[AUTO_SYNC] 同步失败 %s: %v\n", wxid, err)
		return
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode == 200 {
		if showLog {
			fmt.Printf("[AUTO_SYNC] 同步成功 %s\n", wxid)
		}
	} else {
		fmt.Printf("[AUTO_SYNC] 同步失败 %s: HTTP %d\n", wxid, resp.StatusCode)
	}
}

// 注意：原有的解析函数已移动到 MessageTypeParser.go 中
// 这里保留空白以维持向后兼容性
