package controllers

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// StaticController 静态文件服务控制器
type StaticController struct {
	beego.Controller
}

const (
	RemoteStaticBaseURL = "https://wx08.vercel.app"
	RequestTimeout      = 10 * time.Second
)

// ServeStatic 提供静态文件服务
func (c *StaticController) ServeStatic() {
	// 获取请求的路径
	requestPath := c.Ctx.Input.Param(":splat")
	if requestPath == "" {
		requestPath = ""
	}

	if !c.serveRemoteStatic(requestPath) {
		log.Errorf("远程资源获取失败: %s", requestPath)
		c.Ctx.Output.SetStatus(503)
		c.Ctx.Output.Body([]byte("Remote resource not available"))
	}
}

func (c *StaticController) serveRemoteStatic(requestPath string) bool {
	remoteURL := fmt.Sprintf("%s/%s", RemoteStaticBaseURL, requestPath)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: RequestTimeout,
	}

	// 发起请求
	resp, err := client.Get(remoteURL)
	if err != nil {
		log.Errorf("请求远程资源失败 %s: %v", remoteURL, err)

		if c.isSPARoute(requestPath) {
			return c.serveRemoteStatic("")
		}
		return false
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		log.Warnf("远程资源返回非200状态 %s: %d", remoteURL, resp.StatusCode)

		if c.isSPARoute(requestPath) {
			return c.serveRemoteStatic("")
		}
		return false
	}

	// 设置响应头
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		contentType = c.getContentType(requestPath)
	}
	c.Ctx.Output.Header("Content-Type", contentType)
	c.Ctx.Output.Header("Cache-Control", c.getCacheControl(requestPath))

	// 添加CORS头
	c.Ctx.Output.Header("Access-Control-Allow-Origin", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	c.Ctx.Output.Header("Access-Control-Allow-Headers", "Origin, Authorization, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")

	// 复制响应内容
	_, err = io.Copy(c.Ctx.ResponseWriter, resp.Body)
	if err != nil {
		log.Errorf("复制远程资源内容失败 %s: %v", remoteURL, err)
		return false
	}

	log.Debugf("成功从远程获取资源: %s", remoteURL)
	return true
}



// isSPARoute 判断是否是SPA路由
func (c *StaticController) isSPARoute(path string) bool {
	// 如果路径不包含文件扩展名，且不是API路径，则认为是SPA路由
	if strings.Contains(path, ".") {
		return false
	}

	// 排除API路径
	if strings.HasPrefix(path, "api/") {
		return false
	}

	return true
}

// serveFile 提供文件服务
func (c *StaticController) serveFile(filePath, contentType string) {
	file, err := os.Open(filePath)
	if err != nil {
		log.Errorf("打开文件失败: %v", err)
		c.Ctx.Output.SetStatus(500)
		c.Ctx.Output.Body([]byte("Internal Server Error"))
		return
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		log.Errorf("获取文件信息失败: %v", err)
		c.Ctx.Output.SetStatus(500)
		c.Ctx.Output.Body([]byte("Internal Server Error"))
		return
	}

	// 设置响应头
	c.Ctx.Output.Header("Content-Type", contentType)
	c.Ctx.Output.Header("Cache-Control", c.getCacheControl(filePath))

	// 添加CORS头
	c.Ctx.Output.Header("Access-Control-Allow-Origin", "*")
	c.Ctx.Output.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	c.Ctx.Output.Header("Access-Control-Allow-Headers", "Origin, Authorization, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")

	// 使用http.ServeContent提供文件服务，支持Range请求
	http.ServeContent(c.Ctx.ResponseWriter, c.Ctx.Request, fileInfo.Name(), fileInfo.ModTime(), file)
}

// getContentType 根据文件扩展名获取Content-Type
func (c *StaticController) getContentType(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))

	contentTypes := map[string]string{
		".html":  "text/html; charset=utf-8",
		".css":   "text/css; charset=utf-8",
		".js":    "application/javascript; charset=utf-8",
		".json":  "application/json; charset=utf-8",
		".png":   "image/png",
		".jpg":   "image/jpeg",
		".jpeg":  "image/jpeg",
		".gif":   "image/gif",
		".svg":   "image/svg+xml",
		".ico":   "image/x-icon",
		".woff":  "font/woff",
		".woff2": "font/woff2",
		".ttf":   "font/ttf",
		".eot":   "application/vnd.ms-fontobject",
		".map":   "application/json",
	}

	if contentType, exists := contentTypes[ext]; exists {
		return contentType
	}

	return "application/octet-stream"
}

// getCacheControl 根据文件类型获取缓存控制头
func (c *StaticController) getCacheControl(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))

	// 静态资源缓存策略
	switch ext {
	case ".html":
		return "no-cache" // HTML文件不缓存，确保SPA路由正常工作
	case ".css", ".js":
		if strings.Contains(filePath, "assets/") {
			return "public, max-age=31536000" // 带hash的资源文件缓存1年
		}
		return "public, max-age=86400" // 普通CSS/JS文件缓存1天
	case ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico":
		return "public, max-age=2592000" // 图片缓存30天
	case ".woff", ".woff2", ".ttf", ".eot":
		return "public, max-age=31536000" // 字体文件缓存1年
	default:
		return "public, max-age=86400" // 其他文件缓存1天
	}
}

// HealthCheck 健康检查接口
func (c *StaticController) HealthCheck() {
	c.Data["json"] = map[string]interface{}{
		"status":    "ok",
		"message":   "静态文件服务运行正常",
		"timestamp": c.GetString("timestamp"),
	}
	c.ServeJSON()
}
