package comm

import (
	"fmt"
	"strings"

	"github.com/astaxie/beego"
)

// APIDocsConfig API文档配置
type APIDocsConfig struct {
	Route      string // API文档路由路径
	Key        string // API文档访问密钥
	KeyEnabled bool   // 是否启用密钥验证
}

// GetAPIDocsConfig 获取API文档配置
func GetAPIDocsConfig() *APIDocsConfig {
	route := beego.AppConfig.DefaultString("api_docs_route", "/api")

	// 统一的API密钥配置，支持向后兼容
	key := GetUnifiedAPIKey()
	keyEnabled := GetUnifiedAPIKeyEnabled()

	// 确保路由以 / 开头
	if !strings.HasPrefix(route, "/") {
		route = "/" + route
	}

	// 移除末尾的 /
	route = strings.TrimSuffix(route, "/")

	return &APIDocsConfig{
		Route:      route,
		Key:        key,
		KeyEnabled: keyEnabled,
	}
}

// GetUnifiedAPIKey 获取统一的API密钥
// 优先使用新的统一配置，如果不存在则回退到旧配置
func GetUnifiedAPIKey() string {
	// 优先使用新的统一配置
	if key := beego.AppConfig.String("api_key"); key != "" {
		return key
	}
	// 默认值
	return "api_kedaya"
}

// GetUnifiedAPIKeyEnabled 获取统一的API密钥启用状态
// 优先使用新的统一配置，如果不存在则回退到旧配置
func GetUnifiedAPIKeyEnabled() bool {
	// 检查是否存在新的统一配置
	if beego.AppConfig.String("api_key_enabled") != "" {
		return beego.AppConfig.DefaultBool("api_key_enabled", false)
	}
	// 默认禁用（更安全的默认值）
	return false
}

// GetAPIDocsURL 获取API文档完整URL
func GetAPIDocsURL() string {
	config := GetAPIDocsConfig()
	httpAddr := beego.AppConfig.DefaultString("httpaddr", "0.0.0.0")
	httpPort := beego.AppConfig.DefaultInt("httpport", 8059)

	// 如果绑定地址是 0.0.0.0，使用 localhost
	if httpAddr == "0.0.0.0" || httpAddr == "" {
		httpAddr = "localhost"
	}

	return fmt.Sprintf("http://%s:%d%s", httpAddr, httpPort, config.Route)
}
