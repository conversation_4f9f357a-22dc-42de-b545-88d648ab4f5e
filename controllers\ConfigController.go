package controllers

import (
	"bufio"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

// ConfigController 配置管理控制器
// 负责处理系统配置文件的读取和修改操作
// 继承自BaseController，具备统一的错误处理和响应格式化能力
type ConfigController struct {
	BaseController
}

// ConfigItem 配置项结构
type ConfigItem struct {
	Key         string `json:"key"`         // 配置键名
	Value       string `json:"value"`       // 配置值
	Description string `json:"description"` // 配置描述
	Type        string `json:"type"`        // 配置类型：string, int, bool
	Category    string `json:"category"`    // 配置分类
	Sensitive   bool   `json:"sensitive"`   // 是否为敏感配置
}

// ConfigFile 配置文件结构
type ConfigFile struct {
	Name        string       `json:"name"`        // 文件名
	Path        string       `json:"path"`        // 文件路径
	Description string       `json:"description"` // 文件描述
	Items       []ConfigItem `json:"items"`       // 配置项列表
}

// GetConfigs 获取所有配置文件和配置项
//
// 功能说明：
// - 读取conf文件夹下的所有配置文件
// - 解析配置项并添加描述信息
// - 返回结构化的配置数据
//
// @Summary 获取配置列表
// @Description 获取系统所有配置文件和配置项信息，包括配置描述和分类
// @Tags 配置管理
// @Accept json
// @Produce json
// @Success 200 {object} models.ResponseResult{data=[]ConfigFile} "成功返回配置列表"
// @Failure 401 {object} models.ResponseResult "未授权访问"
// @Failure 500 {object} models.ResponseResult "服务器内部错误"
// @router /GetConfigs [get]
func (c *ConfigController) GetConfigs() {
	configFiles, err := c.loadAllConfigFiles()
	if err != nil {
		c.HandleError(err, "获取配置失败")
		return
	}

	c.SendSuccessResponse(configFiles, "获取配置成功")
}

// UpdateConfig 更新配置项
//
// 功能说明：
// - 更新指定配置文件中的配置项
// - 支持修改配置值
// - 自动备份原配置文件
//
// @Summary 更新配置项
// @Description 更新指定配置文件中的配置项值
// @Tags 配置管理
// @Accept json
// @Produce json
// @Param file query string true "配置文件名"
// @Param key query string true "配置键名"
// @Param value query string true "配置值"
// @Success 200 {object} models.ResponseResult "更新成功"
// @Failure 400 {object} models.ResponseResult "参数错误"
// @Failure 401 {object} models.ResponseResult "未授权访问"
// @Failure 500 {object} models.ResponseResult "服务器内部错误"
// @router /UpdateConfig [post]
func (c *ConfigController) UpdateConfig() {
	fileName := c.GetString("file")
	key := c.GetString("key")
	value := c.GetString("value")

	if fileName == "" || key == "" {
		c.SendErrorResponse(400, "文件名和配置键不能为空")
		return
	}

	// 验证文件名安全性
	if !c.isValidFileName(fileName) {
		c.SendErrorResponse(400, "无效的文件名")
		return
	}

	// 检查演示模式 - 演示模式下所有配置项都不能修改，包括demo_mode本身
	if c.isDemoModeEnabled() {
		// 检查是否提供了管理员密钥来禁用演示模式
		adminKey := c.GetString("admin_key")
		if key == "demo_mode" && value == "false" && c.isValidAdminKey(adminKey) {
			log.WithFields(log.Fields{
				"ip":     c.Ctx.Input.IP(),
				"action": "admin_disable_demo_mode",
			}).Info("管理员通过特殊密钥禁用演示模式")
			// 允许管理员禁用演示模式，继续执行
		} else {
			log.WithFields(log.Fields{
				"file":      fileName,
				"key":       key,
				"value":     c.maskSensitiveValue(key, value),
				"ip":        c.Ctx.Input.IP(),
				"admin_key": adminKey != "",
				"action":    "blocked_by_demo_mode",
			}).Warn("演示模式下尝试修改配置被阻止")

			c.SendErrorResponse(403, "演示模式下所有配置项均为只读，无法修改。如需禁用演示模式，请提供管理员密钥")
			return
		}
	}

	// 更新配置
	err := c.updateConfigItem(fileName, key, value)
	if err != nil {
		c.HandleError(err, "更新配置失败")
		return
	}

	// 记录操作日志
	log.WithFields(log.Fields{
		"file":  fileName,
		"key":   key,
		"value": c.maskSensitiveValue(key, value),
		"ip":    c.Ctx.Input.IP(),
	}).Info("配置项已更新")

	c.SendSuccessResponse(nil, "配置更新成功")
}

// GetConfigFile 获取单个配置文件内容
//
// @Summary 获取配置文件
// @Description 获取指定配置文件的详细内容
// @Tags 配置管理
// @Accept json
// @Produce json
// @Param file query string true "配置文件名"
// @Success 200 {object} models.ResponseResult{data=ConfigFile} "成功返回配置文件"
// @Failure 400 {object} models.ResponseResult "参数错误"
// @Failure 401 {object} models.ResponseResult "未授权访问"
// @Failure 404 {object} models.ResponseResult "文件不存在"
// @Failure 500 {object} models.ResponseResult "服务器内部错误"
// @router /GetConfigFile [get]
func (c *ConfigController) GetConfigFile() {
	fileName := c.GetString("file")
	if fileName == "" {
		c.SendErrorResponse(400, "文件名不能为空")
		return
	}

	if !c.isValidFileName(fileName) {
		c.SendErrorResponse(400, "无效的文件名")
		return
	}

	configFile, err := c.loadConfigFile(fileName)
	if err != nil {
		if os.IsNotExist(err) {
			c.SendErrorResponse(404, "配置文件不存在")
		} else {
			c.HandleError(err, "读取配置文件失败")
		}
		return
	}

	c.SendSuccessResponse(configFile, "获取配置文件成功")
}

// loadAllConfigFiles 加载所有配置文件
func (c *ConfigController) loadAllConfigFiles() ([]ConfigFile, error) {
	confDir := "conf"
	files, err := ioutil.ReadDir(confDir)
	if err != nil {
		return nil, fmt.Errorf("读取配置目录失败: %v", err)
	}

	var configFiles []ConfigFile
	for _, file := range files {
		if file.IsDir() || !strings.HasSuffix(file.Name(), ".conf") {
			continue
		}

		configFile, err := c.loadConfigFile(file.Name())
		if err != nil {
			log.WithFields(log.Fields{
				"file":  file.Name(),
				"error": err.Error(),
			}).Warn("加载配置文件失败")
			continue
		}

		configFiles = append(configFiles, *configFile)
	}

	return configFiles, nil
}

// loadConfigFile 加载单个配置文件
func (c *ConfigController) loadConfigFile(fileName string) (*ConfigFile, error) {
	filePath := filepath.Join("conf", fileName)

	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	configFile := &ConfigFile{
		Name:        fileName,
		Path:        filePath,
		Description: c.getFileDescription(fileName),
		Items:       []ConfigItem{},
	}

	// 解析配置文件
	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 跳过注释和空行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 解析配置项
		if strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])

				// 移除引号
				value = strings.Trim(value, `"`)

				item := ConfigItem{
					Key:         key,
					Value:       value,
					Description: c.getConfigDescription(key),
					Type:        c.getConfigType(key),
					Category:    c.getConfigCategory(key),
					Sensitive:   c.isSensitiveConfig(key),
				}

				// 对敏感配置进行遮蔽
				if item.Sensitive {
					item.Value = c.maskSensitiveValue(key, value)
				}

				configFile.Items = append(configFile.Items, item)
			}
		}
	}

	return configFile, nil
}

// updateConfigItem 更新配置项
func (c *ConfigController) updateConfigItem(fileName, key, value string) error {
	filePath := filepath.Join("conf", fileName)

	// 备份原文件
	backupPath := filePath + ".backup"
	if err := c.backupFile(filePath, backupPath); err != nil {
		return fmt.Errorf("备份文件失败: %v", err)
	}

	// 读取文件内容
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	lines := strings.Split(string(content), "\n")
	updated := false

	// 更新配置项
	for i, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if strings.HasPrefix(trimmedLine, key+"=") || strings.HasPrefix(trimmedLine, key+" =") {
			// 保持原有的格式和注释
			if strings.Contains(value, " ") && !strings.HasPrefix(value, `"`) {
				lines[i] = fmt.Sprintf(`%s = "%s"`, key, value)
			} else {
				lines[i] = fmt.Sprintf("%s = %s", key, value)
			}
			updated = true
			break
		}
	}

	// 如果配置项不存在，添加到文件末尾
	if !updated {
		if strings.Contains(value, " ") && !strings.HasPrefix(value, `"`) {
			lines = append(lines, fmt.Sprintf(`%s = "%s"`, key, value))
		} else {
			lines = append(lines, fmt.Sprintf("%s = %s", key, value))
		}
	}

	// 写回文件
	newContent := strings.Join(lines, "\n")
	err = ioutil.WriteFile(filePath, []byte(newContent), 0644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	return nil
}

// backupFile 备份文件
func (c *ConfigController) backupFile(srcPath, dstPath string) error {
	src, err := os.Open(srcPath)
	if err != nil {
		return err
	}
	defer src.Close()

	dst, err := os.Create(dstPath)
	if err != nil {
		return err
	}
	defer dst.Close()

	scanner := bufio.NewScanner(src)
	writer := bufio.NewWriter(dst)
	defer writer.Flush()

	for scanner.Scan() {
		writer.WriteString(scanner.Text() + "\n")
	}

	return scanner.Err()
}

// isValidFileName 验证文件名安全性
func (c *ConfigController) isValidFileName(fileName string) bool {
	// 只允许.conf文件
	if !strings.HasSuffix(fileName, ".conf") {
		return false
	}

	// 防止路径遍历攻击
	if strings.Contains(fileName, "..") || strings.Contains(fileName, "/") || strings.Contains(fileName, "\\") {
		return false
	}

	// 使用正则表达式验证文件名格式
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+\.conf$`, fileName)
	return matched
}

// getFileDescription 获取配置文件描述
func (c *ConfigController) getFileDescription(fileName string) string {
	descriptions := map[string]string{
		"app.conf":           "主配置文件 - 包含应用基本配置",
		"app-http.conf":      "HTTP模式配置文件 - 用于HTTP服务配置",
		"app-tcp.conf":       "TCP模式配置文件 - 用于TCP服务配置",
		"app-optimized.conf": "优化配置文件 - 包含性能优化相关配置",
	}

	if desc, exists := descriptions[fileName]; exists {
		return desc
	}
	return "配置文件"
}

// getConfigDescription 获取配置项描述
func (c *ConfigController) getConfigDescription(key string) string {
	descriptions := map[string]string{
		// 基本配置
		"appname":    "应用名称",
		"httpaddr":   "HTTP服务监听地址",
		"httpport":   "HTTP服务端口",
		"wsport":     "WebSocket服务端口",
		"runmode":    "运行模式 (dev/prod)",
		"deploy_mode": "部署模式 (local/http/tcp/docker)",

		// Redis配置
		"redislink":  "Redis连接地址 - 包含服务器IP和端口信息",
		"redispass":  "Redis密码 - 用于Redis服务器认证",
		"redisdbnum": "Redis数据库编号",

		// 外部服务配置
		"ocrurl":                 "OCR服务地址 - 图像识别服务接口",
		"syncmessagebusinessuri": "消息同步业务URI - 内部业务系统接口",

		// 安全配置
		"api_key":               "统一API密钥 - 用于保护API接口和文档访问",
		"api_key_enabled":       "是否启用API密钥验证",
		"api_docs_route":        "API文档访问路由",
	}

	if desc, exists := descriptions[key]; exists {
		return desc
	}
	return "配置项"
}

// getConfigType 获取配置项类型
func (c *ConfigController) getConfigType(key string) string {
	boolConfigs := []string{"api_key_enabled", "api_docs_key_enabled"}
	intConfigs := []string{"httpport", "wsport", "redisdbnum"}

	for _, boolKey := range boolConfigs {
		if key == boolKey {
			return "bool"
		}
	}

	for _, intKey := range intConfigs {
		if key == intKey {
			return "int"
		}
	}

	return "string"
}

// getConfigCategory 获取配置项分类
func (c *ConfigController) getConfigCategory(key string) string {
	categories := map[string]string{
		"appname":     "基本配置",
		"httpaddr":    "网络配置",
		"httpport":    "网络配置",
		"wsport":      "网络配置",
		"runmode":     "基本配置",
		"deploy_mode": "基本配置",

		"redislink":  "数据库配置",
		"redispass":  "数据库配置",
		"redisdbnum": "数据库配置",

		"ocrurl":                 "外部服务",
		"syncmessagebusinessuri": "外部服务",

		"api_key":              "安全配置",
		"api_key_enabled":      "安全配置",
		"api_docs_route":       "安全配置",
	}

	if category, exists := categories[key]; exists {
		return category
	}
	return "其他配置"
}

// isSensitiveConfig 判断是否为敏感配置
func (c *ConfigController) isSensitiveConfig(key string) bool {
	sensitiveKeys := []string{
		"admin_key",              // 管理员密钥
		"redislink",              // Redis连接地址（包含IP和端口信息）
		"redispass",              // Redis密码
		"api_key",                // 统一API密钥
		"ocrurl",                 // OCR服务地址（可能包含内网IP）
		"syncmessagebusinessuri", // 业务同步URI（可能包含内网地址）
		"logoutbusinessuri",      // 登出业务URI（可能包含内网地址）
	}
	for _, sensitiveKey := range sensitiveKeys {
		if key == sensitiveKey {
			return true
		}
	}
	return false
}

// maskSensitiveValue 遮蔽敏感配置值
func (c *ConfigController) maskSensitiveValue(key, value string) string {
	if !c.isSensitiveConfig(key) {
		return value
	}

	// 空值或过短的值完全遮蔽
	if len(value) <= 4 {
		return strings.Repeat("*", len(value))
	}

	// 根据配置类型采用不同的脱敏策略
	switch key {
	case "redislink":
		// Redis连接地址：保留端口，遮蔽IP
		// 例如：*************:6379 -> ***.***.***.***:6379
		if strings.Contains(value, ":") {
			parts := strings.Split(value, ":")
			if len(parts) == 2 {
				// 遮蔽IP地址
				ip := parts[0]
				port := parts[1]
				if strings.Contains(ip, ".") {
					// IPv4地址脱敏
					ipParts := strings.Split(ip, ".")
					if len(ipParts) == 4 {
						return "***.***.***." + ipParts[3] + ":" + port
					}
				}
				// 其他格式的IP或主机名，保留前后字符
				maskedIP := ip[:1] + strings.Repeat("*", len(ip)-2) + ip[len(ip)-1:]
				return maskedIP + ":" + port
			}
		}
		// 没有端口的情况，按默认方式处理
		return value[:2] + strings.Repeat("*", len(value)-4) + value[len(value)-2:]

	case "ocrurl", "syncmessagebusinessuri":
		// URL类型：保留协议和路径，遮蔽主机部分
		// 例如：http://*************:9050/api -> http://***.***.***:9050/api
		if strings.HasPrefix(value, "http://") || strings.HasPrefix(value, "https://") {
			// 解析URL
			protocolEnd := strings.Index(value, "://") + 3
			protocol := value[:protocolEnd]
			remaining := value[protocolEnd:]

			// 查找主机部分结束位置
			pathStart := strings.Index(remaining, "/")
			if pathStart == -1 {
				pathStart = len(remaining)
			}

			host := remaining[:pathStart]
			path := remaining[pathStart:]

			// 脱敏主机部分
			var maskedHost string
			if strings.Contains(host, ":") {
				// 有端口的情况
				hostParts := strings.Split(host, ":")
				hostPart := hostParts[0]
				port := hostParts[1]

				if strings.Contains(hostPart, ".") {
					// IP地址
					ipParts := strings.Split(hostPart, ".")
					if len(ipParts) == 4 {
						maskedHost = "***.***.***." + ipParts[3] + ":" + port
					} else {
						maskedHost = hostPart[:1] + strings.Repeat("*", len(hostPart)-2) + hostPart[len(hostPart)-1:] + ":" + port
					}
				} else {
					// 域名
					maskedHost = hostPart[:1] + strings.Repeat("*", len(hostPart)-2) + hostPart[len(hostPart)-1:] + ":" + port
				}
			} else {
				// 没有端口
				if strings.Contains(host, ".") && len(strings.Split(host, ".")) == 4 {
					// IP地址
					ipParts := strings.Split(host, ".")
					maskedHost = "***.***.***." + ipParts[3]
				} else {
					// 域名或其他格式
					maskedHost = host[:1] + strings.Repeat("*", len(host)-2) + host[len(host)-1:]
				}
			}

			return protocol + maskedHost + path
		}
		// 不是标准URL格式，按默认方式处理
		return value[:2] + strings.Repeat("*", len(value)-4) + value[len(value)-2:]

	default:
		// 密码等其他敏感信息：保留前后字符，中间用*替代
		return value[:2] + strings.Repeat("*", len(value)-4) + value[len(value)-2:]
	}
}

// isDemoModeEnabled 检查演示模式是否启用
func (c *ConfigController) isDemoModeEnabled() bool {
	// 从app.conf配置文件中读取demo_mode配置项
	configFile, err := c.loadConfigFile("app.conf")
	if err != nil {
		log.WithError(err).Warn("无法加载app.conf文件，假设演示模式未启用")
		return false
	}

	// 查找demo_mode配置项
	for _, item := range configFile.Items {
		if item.Key == "demo_mode" {
			// 处理布尔值，支持字符串形式
			demoMode := item.Value == "true" || item.Value == "1" || item.Value == "yes"
			if demoMode {
				log.WithFields(log.Fields{
					"demo_mode": item.Value,
					"ip":        c.Ctx.Input.IP(),
				}).Info("检测到演示模式已启用，阻止配置修改")
			}
			return demoMode
		}
	}

	// 如果没有找到demo_mode配置项，默认为未启用
	return false
}

// isValidAdminKey 验证管理员密钥
func (c *ConfigController) isValidAdminKey(adminKey string) bool {
	if adminKey == "" {
		return false
	}

	// 从配置中获取管理员密钥，如果没有配置则使用默认值
	expectedAdminKey := beego.AppConfig.DefaultString("admin_key", "admin_kedaya_2024")

	// 验证管理员密钥
	isValid := adminKey == expectedAdminKey

	if !isValid {
		log.WithFields(log.Fields{
			"provided_key": adminKey,
			"ip":          c.Ctx.Input.IP(),
			"action":      "invalid_admin_key",
		}).Warn("无效的管理员密钥")
	}

	return isValid
}
