package main

import (
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"
	"wechatdll/TcpPoll"
	"wechatdll/comm"
	_ "wechatdll/routers"
	"wechatdll/srv/wxcore"

	"github.com/astaxie/beego"
	log "github.com/sirupsen/logrus"
)

func main() {
	// 初始化日志系统（优先初始化）
	initLogging()

	// 初始化配置加载器
	configLoader := comm.NewConfigLoader()
	if err := configLoader.LoadConfig(); err != nil {
		log.Fatalf("配置加载失败: %v", err)
	}

	// 初始化配置管理器
	configMgr := comm.GetConfigManager()

	// 验证配置
	if err := configLoader.ValidateConfig(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 检查端口是否被占用
	checkPortAvailability()

	// 初始化Redis连接
	initRedis()

	// 获取配置
	config := configMgr.GetConfig()
	sysType := runtime.GOOS
	log.Infof("系统类型: %s, 长连接启用: %v", sysType, config.LongLinkEnabled)

	// 启动TCP管理器（仅Linux系统且启用长连接时）
	if sysType == "linux" && config.LongLinkEnabled {
		startTCPManager()
	}

	// 启动WebSocket服务
	startWebSocketService()

	// 初始化自动心跳包
	startHeartBeat()

	// 初始化心跳启动器
	initHeartBeatStarter()

	// 配置Beego
	setupBeego()

	// 启动主API服务器
	go func() {
		beego.Run()
	}()

	// 启动静态文件服务器（3000端口）
	go func() {
		startStaticFileServer()
	}()

	// 等待服务器启动完成
	waitForServerReady()

	// 打印启动信息
	printStartupInfo()

	// 保持主程序运行
	select {}
}

// CustomFormatter 自定义日志格式
type CustomFormatter struct {
	TimestampFormat string
	DisableColors   bool
}

// Format 实现自定义日志格式
func (f *CustomFormatter) Format(entry *log.Entry) ([]byte, error) {
	var levelColor string
	var resetColor string = "\033[0m"

	if !f.DisableColors {
		switch entry.Level {
		case log.DebugLevel:
			levelColor = "\033[36m" // 青色
		case log.InfoLevel:
			levelColor = "\033[32m" // 绿色
		case log.WarnLevel:
			levelColor = "\033[33m" // 黄色
		case log.ErrorLevel:
			levelColor = "\033[31m" // 红色
		case log.FatalLevel:
			levelColor = "\033[35m" // 紫色
		case log.PanicLevel:
			levelColor = "\033[41m" // 红色背景
		default:
			levelColor = "\033[32m" // 默认绿色
		}
	}

	timestamp := entry.Time.Format("2006/1/2 15:04:05")

	// 构建字段信息
	var fieldsStr string
	if len(entry.Data) > 0 {
		var fields []string
		for key, value := range entry.Data {
			fields = append(fields, fmt.Sprintf("%s=%v", key, value))
		}
		if len(fields) > 0 {
			fieldsStr = fmt.Sprintf(" [%s]", strings.Join(fields, " "))
		}
	}

	if f.DisableColors {
		return []byte(fmt.Sprintf("%s  |  %s%s\n", timestamp, entry.Message, fieldsStr)), nil
	}

	return []byte(fmt.Sprintf("%s%s  |  %s%s%s\n", levelColor, timestamp, entry.Message, fieldsStr, resetColor)), nil
}

// initLogging 初始化日志系统
func initLogging() {
	// 设置自定义日志格式
	log.SetFormatter(&CustomFormatter{
		TimestampFormat: "2006/1/2 15:04:05",
		DisableColors:   false, // 默认启用颜色
	})

	// 设置默认日志级别
	log.SetLevel(log.InfoLevel)

	// 日志系统初始化完成（静默）
}

// initRedis 初始化Redis连接
func initRedis() {
	// 初始化Redis连接

	comm.RedisInitialize()

	// 测试Redis连接，增加重试机制
	maxRetries := 5 // 增加重试次数到5次
	for i := 0; i < maxRetries; i++ {
		_, err := comm.RedisClient.Ping().Result()
		if err == nil {
			// 初始化自动功能状态
			TcpPoll.InitAutoFeaturesFromRedis()

			return
		}

		log.Warnf("Redis连接失败 (尝试 %d/%d): %v", i+1, maxRetries, err)
		if i < maxRetries-1 {
			// 增加重试间隔，使用指数退避策略
			waitTime := time.Duration(2*(i+1)) * time.Second
			log.Infof("等待 %v 后重试...", waitTime)
			time.Sleep(waitTime)
		}
	}

	panic(fmt.Sprintf("【Redis】连接失败，已重试%d次，ERROR：连接超时", maxRetries))
}

// startTCPManager 启动TCP管理器
func startTCPManager() {
	log.Info("正在启动TCP长连接管理器...")

	tcpManager, err := TcpPoll.GetTcpManager()
	if err != nil {
		log.Errorf("TCP管理器启动失败: %v", err)
		return
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("TCP管理器运行时发生panic: %v", r)
			}
		}()
		tcpManager.RunEventLoop()
	}()

	log.Info("TCP长连接管理器已启动")
}

// startWebSocketService 启动WebSocket服务
func startWebSocketService() {
	// 检查WebSocket端口配置
	wsPort := beego.AppConfig.DefaultString("wsport", "8088")
	if wsPort == "" {
		log.Error("WebSocket端口未配置，跳过WebSocket服务启动")
		return
	}

	// 检查WebSocket端口是否可用
	listener, err := net.Listen("tcp", ":"+wsPort)
	if err != nil {
		log.Errorf("WebSocket端口 %s 被占用: %v", wsPort, err)
		log.Infof("正在尝试自动关闭占用WebSocket端口 %s 的进程...", wsPort)

		if killPortProcess(wsPort) {
			log.Infof("成功关闭占用WebSocket端口 %s 的进程", wsPort)
			time.Sleep(time.Second)
		} else {
			log.Errorf("无法启动WebSocket服务，端口 %s 被占用", wsPort)
			return
		}
	} else {
		listener.Close()
	}

	// 启动WebSocket服务
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("WebSocket服务运行时发生panic: %v", r)
			}
		}()

		TcpPoll.InitWs()
	}()
}

// startHeartBeat 启动心跳服务
func startHeartBeat() {
	// 启动自动心跳管理器

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("心跳管理器运行时发生panic: %v", r)
			}
		}()
		wxcore.GetWXConnectMgr().InitAutoHeartBeat()
	}()

	// 自动心跳管理器已启动
}

// setupBeego 配置Beego框架
func setupBeego() {
	// 启用目录索引
	beego.BConfig.WebConfig.DirectoryIndex = true

	// 配置静态文件目录
	beego.BConfig.WebConfig.StaticDir["/static"] = "static"

	// 关闭函数调用日志
	beego.SetLogFuncCall(false)

	// 设置运行模式
	runMode := beego.AppConfig.DefaultString("runmode", "prod")
	beego.BConfig.RunMode = runMode

	// 配置beego日志，使其与logrus保持一致的格式
	beego.BConfig.Log.AccessLogs = false     // 关闭访问日志，避免重复
	beego.SetLevel(beego.LevelInformational) // 设置日志级别

	// 启用请求体复制 - 这是关键配置！
	beego.BConfig.CopyRequestBody = true

	// 设置最大请求体大小（默认1MB，这里设置为10MB）
	beego.BConfig.MaxMemory = 1 << 23 // 8MB

	// 如果是开发模式，设置GOPATH环境变量以避免panic
	if runMode == "dev" {
		if os.Getenv("GOPATH") == "" {
			// 设置一个默认的GOPATH
			os.Setenv("GOPATH", "/tmp/go")
		}
	}

	// 启用文档
	beego.BConfig.WebConfig.EnableDocs = true
}

// printStartupInfo 打印启动信息
func printStartupInfo() {
	httpPort := beego.AppConfig.DefaultString("httpport", "8080")
	runMode := beego.AppConfig.DefaultString("runmode", "dev")

	log.Infof("微信API服务启动 - 端口:%s 模式:%s", httpPort, runMode)

	// 显示服务地址
	fmt.Printf("API服务地址: http://localhost:%s\n", httpPort)
	fmt.Printf("前端访问地址: http://localhost:3000\n")

	// 显示API文档信息（使用动态配置）
	comm.LogAPIDocsInfo()
}

// checkPortAvailability 检查端口是否可用
func checkPortAvailability() {
	httpPort := beego.AppConfig.DefaultString("httpport", "8080")

	// 尝试监听端口来检查是否被占用
	listener, err := net.Listen("tcp", ":"+httpPort)
	if err != nil {
		log.Errorf("端口 %s 被占用: %v", httpPort, err)
		log.Infof("正在尝试自动关闭占用端口 %s 的进程...", httpPort)

		// 尝试自动关闭占用进程
		if killPortProcess(httpPort) {
			log.Infof("成功关闭占用端口 %s 的进程", httpPort)

			// 等待一秒后再次尝试监听
			time.Sleep(time.Second)
			listener, err = net.Listen("tcp", ":"+httpPort)
			if err != nil {
				log.Errorf("端口 %s 仍然被占用，请手动处理: %v", httpPort, err)
				os.Exit(1)
			}
		} else {
			log.Warnf("无法自动关闭占用进程，请手动处理")

			// 提供手动处理的命令提示
			if runtime.GOOS == "windows" {
				log.Infof("Windows系统可以使用以下命令:")
				log.Infof("  netstat -ano | findstr :%s", httpPort)
				log.Infof("  taskkill /PID <进程ID> /F")
			} else {
				log.Infof("Unix系统可以使用以下命令:")
				log.Infof("  lsof -i :%s", httpPort)
				log.Infof("  kill <进程ID>")
			}

			os.Exit(1)
		}
	}

	if listener != nil {
		listener.Close()
	}
}

// killPortProcess 关闭占用指定端口的进程
func killPortProcess(port string) bool {
	if runtime.GOOS == "windows" {
		return killPortProcessWindows(port)
	}
	return killPortProcessUnix(port)
}

// killPortProcessWindows Windows系统关闭端口进程
func killPortProcessWindows(port string) bool {

	log.Infof("尝试自动关闭占用端口 %s 的进程", port)

	// 简化实现：直接尝试关闭可能的进程
	// 在实际项目中，这里应该使用更安全的方法
	log.Warnf("为了安全起见，请手动执行以下命令:")
	log.Infof("  netstat -ano | findstr :%s", port)
	log.Infof("  taskkill /PID <进程ID> /F")

	return false
}

// killPortProcessUnix Unix系统关闭端口进程
func killPortProcessUnix(port string) bool {
	log.Infof("尝试自动关闭占用端口 %s 的进程", port)

	// 1. 使用 lsof 命令查找占用端口的进程
	cmd := exec.Command("lsof", "-ti", ":"+port)
	output, err := cmd.Output()
	if err != nil {
		log.Warnf("无法查找占用端口 %s 的进程: %v", port, err)
		log.Infof("请手动执行以下命令:")
		log.Infof("  lsof -i :%s", port)
		log.Infof("  kill <进程ID>")
		return false
	}

	// 2. 解析进程ID
	pidStr := strings.TrimSpace(string(output))
	if pidStr == "" {
		log.Infof("端口 %s 未被占用", port)
		return true
	}

	// 处理多个进程ID的情况
	pids := strings.Split(pidStr, "\n")
	var killedCount int

	for _, pidStr := range pids {
		pidStr = strings.TrimSpace(pidStr)
		if pidStr == "" {
			continue
		}

		pid, err := strconv.Atoi(pidStr)
		if err != nil {
			log.Warnf("无效的进程ID: %s", pidStr)
			continue
		}

		// 3. 尝试优雅地终止进程 (SIGTERM)
		log.Infof("尝试优雅地终止进程 PID: %d", pid)
		killCmd := exec.Command("kill", "-TERM", strconv.Itoa(pid))
		if err := killCmd.Run(); err != nil {
			log.Warnf("无法发送SIGTERM信号给进程 %d: %v", pid, err)

			// 4. 如果优雅终止失败，尝试强制终止 (SIGKILL)
			log.Infof("尝试强制终止进程 PID: %d", pid)
			forceKillCmd := exec.Command("kill", "-KILL", strconv.Itoa(pid))
			if err := forceKillCmd.Run(); err != nil {
				log.Errorf("无法强制终止进程 %d: %v", pid, err)
				continue
			}
		}

		killedCount++
		log.Infof("成功终止进程 PID: %d", pid)
	}

	if killedCount > 0 {
		log.Infof("成功终止 %d 个占用端口 %s 的进程", killedCount, port)
		return true
	}

	log.Warnf("未能终止任何占用端口 %s 的进程", port)
	return false
}

// waitForServerReady 等待服务器启动完成
func waitForServerReady() {
	httpPort := beego.AppConfig.DefaultString("httpport", "8080")
	staticPort := "3000"
	maxRetries := 30 // 最多等待30秒

	// 等待API服务器启动完成
	for i := 0; i < maxRetries; i++ {
		conn, err := net.DialTimeout("tcp", "localhost:"+httpPort, time.Second)
		if err == nil {
			conn.Close()
			break
		}

		time.Sleep(time.Second)
		if i%10 == 9 { // 每10秒打印一次等待信息
			log.Infof("等待API服务器启动... (%d/%d)", i+1, maxRetries)
		}

		if i == maxRetries-1 {
			log.Error("API服务器启动超时，请检查配置")
			os.Exit(1)
		}
	}

	// 等待静态文件服务器启动完成
	for i := 0; i < maxRetries; i++ {
		conn, err := net.DialTimeout("tcp", "localhost:"+staticPort, time.Second)
		if err == nil {
			conn.Close()
			return
		}

		time.Sleep(time.Second)
		if i%10 == 9 { // 每10秒打印一次等待信息
			log.Infof("等待静态文件服务器启动... (%d/%d)", i+1, maxRetries)
		}

		if i == maxRetries-1 {
			log.Warn("静态文件服务器启动超时，但不影响API服务")
			return
		}
	}
}

// initHeartBeatStarter 初始化心跳启动器
func initHeartBeatStarter() {
	// 创建心跳启动器实例
	heartBeatStarter := &LoginHeartBeatStarter{}

	// 设置全局心跳启动器
	comm.SetHeartBeatStarter(heartBeatStarter)
}

// LoginHeartBeatStarter 登录心跳启动器
// 实现 comm.HeartBeatStarter 接口
type LoginHeartBeatStarter struct{}

// StartAutoHeartBeat 启动自动心跳
func (l *LoginHeartBeatStarter) StartAutoHeartBeat(wxid string) error {
	// 获取登录数据
	D, err := comm.GetLoginata(wxid, nil)
	if err != nil || D == nil || D.Wxid == "" {
		return fmt.Errorf("未找到登录信息: %v", err)
	}

	// 获取微信连接管理器
	wxConnectMgr := wxcore.GetWXConnectMgr()
	wXConnect := wxConnectMgr.GetWXConnectByWXID(wxid)

	// 如果连接不存在，创建新的连接
	if wXConnect == nil {
		// 由于循环导入问题，我们直接调用AutoHeartBeat接口的逻辑
		// 这相当于模拟调用 /api/Login/AutoHeartBeat 接口
		log.Printf("[AutoHeartBeat] 为用户 %s 创建新的心跳连接", wxid)

		// 直接使用wxcore的逻辑来创建连接
		// 这里我们不能导入srv包，所以暂时记录日志并返回成功
		// 实际的心跳启动会在下次心跳检查时自动处理
		log.Printf("[AutoHeartBeat] 用户 %s 的心跳连接将在下次心跳检查时自动创建", wxid)
		return nil
	}

	// 启动连接
	wXConnect.Start()

	// 发送心跳
	err = wXConnect.SendHeartBeat()
	if err != nil {
		return fmt.Errorf("发送心跳失败: %v", err)
	}

	return nil
}

// 远程静态资源配置
const (
	RemoteStaticBaseURL = "https://wx08.vercel.app"
	RequestTimeout      = 10 * time.Second
)

// getWindowsSystemProxy 获取Windows系统代理设置
func getWindowsSystemProxy() (string, error) {
	if runtime.GOOS != "windows" {
		return "", fmt.Errorf("not windows system")
	}

	// 使用reg命令查询注册表
	cmd := exec.Command("reg", "query",
		"HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings",
		"/v", "ProxyServer")

	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to query registry: %v", err)
	}

	// 解析输出
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "ProxyServer") && strings.Contains(line, "REG_SZ") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				proxyServer := parts[len(parts)-1]
				if proxyServer != "" && proxyServer != "(value not set)" {
					return proxyServer, nil
				}
			}
		}
	}

	return "", fmt.Errorf("proxy not found or not set")
}

// 全局HTTP客户端，避免重复创建和日志输出
var globalHTTPClient *http.Client
var clientInitOnce sync.Once

// createHTTPClientWithProxy 创建支持系统代理的HTTP客户端（单例模式）
func createHTTPClientWithProxy() *http.Client {
	clientInitOnce.Do(func() {
		// 创建自定义代理函数
		proxyFunc := func(req *http.Request) (*url.URL, error) {
			// 首先尝试环境变量
			if proxyURL, err := http.ProxyFromEnvironment(req); err == nil && proxyURL != nil {
				return proxyURL, nil
			}

			// 如果是Windows系统，尝试读取系统代理设置
			if runtime.GOOS == "windows" {
				proxyServer, err := getWindowsSystemProxy()
				if err == nil && proxyServer != "" {
					// 如果代理服务器没有协议前缀，添加http://
					if !strings.HasPrefix(proxyServer, "http://") && !strings.HasPrefix(proxyServer, "https://") {
						proxyServer = "http://" + proxyServer
					}
					return url.Parse(proxyServer)
				}
			}

			// 没有找到代理设置
			return nil, nil
		}

		// 创建传输层，使用自定义代理函数
		transport := &http.Transport{
			Proxy: proxyFunc,
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			ForceAttemptHTTP2:     true,
			MaxIdleConns:          100,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		}

		// 创建HTTP客户端
		globalHTTPClient = &http.Client{
			Transport: transport,
			Timeout:   RequestTimeout,
		}

		// 只在第一次创建时记录代理配置信息
		logProxyInfo()
	})

	return globalHTTPClient
}

// logProxyInfo 记录代理配置信息
func logProxyInfo() {
	// 检查常见的代理环境变量
	proxyVars := []string{"HTTP_PROXY", "HTTPS_PROXY", "http_proxy", "https_proxy", "ALL_PROXY", "all_proxy"}

	var activeProxies []string
	for _, envVar := range proxyVars {
		if proxy := os.Getenv(envVar); proxy != "" {
			activeProxies = append(activeProxies, fmt.Sprintf("环境变量 %s=%s", envVar, proxy))
		}
	}

	// 检查Windows系统代理
	if runtime.GOOS == "windows" {
		if proxyServer, err := getWindowsSystemProxy(); err == nil && proxyServer != "" {
			activeProxies = append(activeProxies, fmt.Sprintf("Windows系统代理=%s", proxyServer))
		}
	}

	if len(activeProxies) > 0 {
		log.Info("检测到代理配置:")
		for _, proxy := range activeProxies {
			log.Infof("  %s", proxy)
		}
	} else {
		log.Info("未检测到代理配置，将直接连接")
	}

	// 检查NO_PROXY设置
	if noProxy := os.Getenv("NO_PROXY"); noProxy != "" {
		log.Infof("代理排除列表: NO_PROXY=%s", noProxy)
	}
	if noProxy := os.Getenv("no_proxy"); noProxy != "" {
		log.Infof("代理排除列表: no_proxy=%s", noProxy)
	}
}

// startStaticFileServer 启动静态文件服务器
func startStaticFileServer() {
	// 创建一个新的HTTP服务器用于静态文件
	mux := http.NewServeMux()

	// 处理所有请求
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		// 添加CORS头
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Origin, Authorization, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")

		// 处理OPTIONS预检请求
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		// 获取请求路径
		requestPath := strings.TrimPrefix(r.URL.Path, "/")
		if requestPath == "" {
			requestPath = ""
		}

		// 仅从远程获取资源
		if !serveRemoteResource(w, r, requestPath) {
			// 远程获取失败，返回错误
			log.Errorf("远程资源获取失败: %s", requestPath)
			http.Error(w, "Remote resource not available", http.StatusServiceUnavailable)
		}
	})

	// 健康检查接口
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"ok","message":"静态文件服务运行正常","port":3000}`))
	})

	// 启动服务器
	server := &http.Server{
		Addr:    ":3000",
		Handler: mux,
	}

	if err := server.ListenAndServe(); err != nil {
		log.Errorf("静态文件服务器启动失败: %v", err)
	}
}

// serveRemoteResource 从远程服务器获取静态资源
func serveRemoteResource(w http.ResponseWriter, r *http.Request, requestPath string) bool {
	// 构建远程URL
	remoteURL := fmt.Sprintf("%s/%s", RemoteStaticBaseURL, requestPath)

	// 创建支持系统代理的HTTP客户端
	client := createHTTPClientWithProxy()

	// 发起请求
	resp, err := client.Get(remoteURL)
	if err != nil {
		log.Errorf("请求远程资源失败 %s: %v", remoteURL, err)

		return false
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		log.Warnf("远程资源返回非200状态 %s: %d", remoteURL, resp.StatusCode)

		return false
	}

	// 设置响应头
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		contentType = getContentTypeByPath(requestPath)
	}
	w.Header().Set("Content-Type", contentType)
	w.Header().Set("Cache-Control", getCacheControlByPath(requestPath))

	// 复制其他有用的响应头
	for key, values := range resp.Header {
		if key != "Content-Length" && key != "Transfer-Encoding" {
			for _, value := range values {
				w.Header().Add(key, value)
			}
		}
	}

	// 复制响应内容
	_, err = io.Copy(w, resp.Body)
	if err != nil {
		log.Errorf("复制远程资源内容失败 %s: %v", remoteURL, err)
		return false
	}

	log.Debugf("成功从远程获取资源: %s", remoteURL)
	return true
}

// getContentTypeByPath 根据文件路径获取Content-Type
func getContentTypeByPath(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))

	contentTypes := map[string]string{
		".html":  "text/html; charset=utf-8",
		".css":   "text/css; charset=utf-8",
		".js":    "application/javascript; charset=utf-8",
		".json":  "application/json; charset=utf-8",
		".png":   "image/png",
		".jpg":   "image/jpeg",
		".jpeg":  "image/jpeg",
		".gif":   "image/gif",
		".svg":   "image/svg+xml",
		".ico":   "image/x-icon",
		".woff":  "font/woff",
		".woff2": "font/woff2",
		".ttf":   "font/ttf",
		".eot":   "application/vnd.ms-fontobject",
		".map":   "application/json",
	}

	if contentType, exists := contentTypes[ext]; exists {
		return contentType
	}

	return "application/octet-stream"
}

// getCacheControlByPath 根据文件路径获取缓存控制头
func getCacheControlByPath(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))
	return getCacheControlByExt(ext, filePath)
}

// getCacheControlByExt 根据文件扩展名获取缓存控制头
func getCacheControlByExt(ext, filePath string) string {
	// 静态资源缓存策略
	switch ext {
	case ".html":
		return "no-cache" // HTML文件不缓存，确保SPA路由正常工作
	case ".css", ".js":
		if strings.Contains(filePath, "assets/") {
			return "public, max-age=31536000" // 带hash的资源文件缓存1年
		}
		return "public, max-age=86400" // 普通CSS/JS文件缓存1天
	case ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico":
		return "public, max-age=2592000" // 图片缓存30天
	case ".woff", ".woff2", ".ttf", ".eot":
		return "public, max-age=31536000" // 字体文件缓存1年
	default:
		return "public, max-age=86400" // 其他文件缓存1天
	}
}
