package Friend

import (
	"fmt"
	"wechatdll/Algorithm"
	"wechatdll/Cilent/mm"
	"wechatdll/bts"
	"wechatdll/comm"
	"wechatdll/models"

	"github.com/golang/protobuf/proto"
)

func Delete(Data DefaultParam) models.ResponseResult {
	D, err := comm.GetLoginata(Data.Wxid, nil)
	if err != nil || D == nil || D.Wxid == "" {
		errorMsg := fmt.Sprintf("异常：%v [%v]", "未找到登录信息", Data.Wxid)
		if err != nil {
			errorMsg = fmt.Sprintf("异常：%v", err.Error())
		}
		return models.ResponseResult{
			Code:    -8,
			Success: false,
			Message: errorMsg,
			Data:    nil,
		}
	}

	//先获取用户的基本信息
	getContact := GetContractDetail(GetContractDetailparameter{
		Wxid:     Data.Wxid,
		Towxids:  Data.ToWxid,
		ChatRoom: "",
	})

	if getContact.Code != 0 {
		return getContact
	}

	// 处理GetContractDetail返回的不同数据类型
	var Contact mm.GetContactResponse

	// 检查返回的数据类型
	switch data := getContact.Data.(type) {
	case mm.GetContactResponse:
		// 直接是GetContactResponse结构体（从API返回）
		Contact = data
	case []interface{}:
		// 数组格式（从缓存返回），需要转换为GetContactResponse格式
		if len(data) > 0 {
			// 尝试将第一个元素转换为ModContact
			if contactMap, ok := data[0].(map[string]interface{}); ok {
				// 使用JSON转换来处理复杂的数据结构转换
				jsonHelper := comm.GetJSONHelper()
				var modContact mm.ModContact
				if err := jsonHelper.ConvertType(contactMap, &modContact); err != nil {
					return models.ResponseResult{
						Code:    -8,
						Success: false,
						Message: fmt.Sprintf("联系人数据转换失败：%v", err.Error()),
						Data:    nil,
					}
				}
				// 需要将ModContact转换为ModContacts类型
				// 处理DomainList类型转换：ModContact中是[]*SKBuiltinStringT，ModContacts中是*SKBuiltinStringT
				var domainList *mm.SKBuiltinStringT
				if modContact.DomainList != nil && len(modContact.DomainList) > 0 {
					domainList = modContact.DomainList[0] // 取第一个元素
				}

				modContacts := &mm.ModContacts{
					UserName:        modContact.UserName,
					NickName:        modContact.NickName,
					Pyinitial:       modContact.PyInitial,
					QuanPin:         modContact.QuanPin,
					Sex:             modContact.Sex,
					ImgBuf:          modContact.ImgBuf,
					BitMask:         modContact.BitMask,
					BitVal:          modContact.BitVal,
					ImgFlag:         modContact.ImgFlag,
					Remark:          modContact.Remark,
					RemarkPyinitial: modContact.RemarkPyinitial,
					RemarkQuanPin:   modContact.RemarkQuanPin,
					ContactType:     modContact.ContactType,
					RoomInfoCount:   modContact.RoomInfoCount,
					RoomInfoList:    modContact.RoomInfoList,
					DomainList:      domainList,
				}

				// 构造GetContactResponse结构
				Contact = mm.GetContactResponse{
					ContactList: []*mm.ModContacts{modContacts},
				}
			} else {
				return models.ResponseResult{
					Code:    -8,
					Success: false,
					Message: "联系人数据格式错误：无法解析联系人信息",
					Data:    nil,
				}
			}
		} else {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: "未找到联系人信息",
				Data:    nil,
			}
		}
	default:
		// 尝试使用原有的转换方法作为后备
		Contact = bts.GetContactResponse(getContact.Data)
	}

	if len(Contact.ContactList) > 0 {
		modContact := Contact.ContactList[0]

		// 处理DomainList类型转换：ModContacts中是*SKBuiltinStringT，ModContact中是[]*SKBuiltinStringT
		var domainList []*mm.SKBuiltinStringT
		if modContact.DomainList != nil {
			// 将单个DomainList转换为数组格式
			domainList = []*mm.SKBuiltinStringT{modContact.DomainList}
		}

		ContactList := &mm.ModContact{
			UserName:        modContact.UserName,
			NickName:        modContact.NickName,
			PyInitial:       modContact.Pyinitial,
			QuanPin:         modContact.QuanPin,
			Sex:             modContact.Sex,
			ImgBuf:          modContact.ImgBuf,
			BitMask:         modContact.BitMask,
			BitVal:          proto.Uint32(6),
			ImgFlag:         modContact.ImgFlag,
			Remark:          modContact.Remark,
			RemarkPyinitial: modContact.RemarkPyinitial,
			RemarkQuanPin:   modContact.RemarkQuanPin,
			ContactType:     modContact.ContactType,
			ChatRoomNotify:  proto.Uint32(1),
			AddContactScene: modContact.AddContactScene,
			Extflag:         proto.Int32(int32(*modContact.ExtFlag)),
			DomainList:      domainList, // 添加DomainList字段处理
		}

		var cmdItems []*mm.CmdItem
		buffer, err := proto.Marshal(ContactList)
		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("系统异常：%v", err.Error()),
				Data:    nil,
			}
		}

		cmdItem := mm.CmdItem{
			CmdId: proto.Int32(2),
			CmdBuf: &mm.SKBuiltinBufferT{
				ILen:   proto.Uint32(uint32(len(buffer))),
				Buffer: buffer,
			},
		}
		cmdItems = append(cmdItems, &cmdItem)

		req := &mm.OpLogRequest{
			Cmd: &mm.CmdList{
				Count: proto.Uint32(uint32(len(cmdItems))),
				List:  cmdItems,
			},
		}

		reqdata, err := proto.Marshal(req)

		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("系统异常：%v", err.Error()),
				Data:    nil,
			}
		}

		//发包
		protobufdata, _, errtype, err := comm.SendRequest(comm.SendPostData{
			Ip:     D.Mmtlsip,
			Host:   D.ShortHost,
			Cgiurl: "/cgi-bin/micromsg-bin/oplog",
			Proxy:  D.Proxy,
			PackData: Algorithm.PackData{
				Reqdata:          reqdata,
				Cgi:              681,
				Uin:              D.Uin,
				Cookie:           D.Cooike,
				Sessionkey:       D.Sessionkey,
				EncryptType:      5,
				Loginecdhkey:     D.RsaPublicKey,
				Clientsessionkey: D.Clientsessionkey,
				UseCompress:      false,
			},
		}, D.MmtlsKey)

		if err != nil {
			return models.ResponseResult{
				Code:    errtype,
				Success: false,
				Message: err.Error(),
				Data:    nil,
			}
		}

		//解包
		Response := mm.OplogResponse{}
		err = proto.Unmarshal(protobufdata, &Response)

		if err != nil {
			return models.ResponseResult{
				Code:    -8,
				Success: false,
				Message: fmt.Sprintf("反序列化失败：%v", err.Error()),
				Data:    nil,
			}
		}

		return models.ResponseResult{
			Code:    0,
			Success: true,
			Message: "成功",
			Data:    Response,
		}

	}

	return models.ResponseResult{}

}
